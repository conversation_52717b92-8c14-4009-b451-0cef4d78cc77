#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试专业系统状态
"""

import requests
import json

def test_professional_system():
    """测试专业系统状态"""
    print("🔍 调试专业系统状态...")
    
    # 1. 测试模块导入
    print("\n1️⃣ 测试模块导入:")
    try:
        from data_processing.professional_data_processor import ProfessionalDataProcessor, TechnicalDocument
        print("   ✅ ProfessionalDataProcessor 导入成功")
    except Exception as e:
        print(f"   ❌ ProfessionalDataProcessor 导入失败: {e}")
    
    try:
        from langchain_modules.prompts.professional_prompt_engine import ProfessionalPromptEngine, ContextualInformation
        print("   ✅ ProfessionalPromptEngine 导入成功")
    except Exception as e:
        print(f"   ❌ ProfessionalPromptEngine 导入失败: {e}")
    
    try:
        from retriever.advanced_professional_retriever import AdvancedProfessionalRetriever, RetrievalResult
        print("   ✅ AdvancedProfessionalRetriever 导入成功")
    except Exception as e:
        print(f"   ❌ AdvancedProfessionalRetriever 导入失败: {e}")
    
    # 2. 测试服务器状态
    print("\n2️⃣ 测试服务器状态:")
    try:
        response = requests.get('http://localhost:5002/api/v1/system/complete-status', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 服务器响应正常")
            
            # 检查专业系统状态
            prof_available = data.get('professional_system_available', False)
            prof_processor = data.get('professional_data_processor', False)
            prof_engine = data.get('professional_prompt_engine', False)
            prof_retriever = data.get('advanced_retriever', False)
            
            print(f"   专业系统可用: {prof_available}")
            print(f"   专业数据处理器: {prof_processor}")
            print(f"   专业提示词引擎: {prof_engine}")
            print(f"   高级检索器: {prof_retriever}")
            
            if prof_available:
                print("   🎉 专业系统在服务器中可用！")
            else:
                print("   ⚠️ 专业系统在服务器中不可用")
                
        else:
            print(f"   ❌ 服务器响应异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 服务器连接失败: {e}")
    
    # 3. 测试故障分析功能
    print("\n3️⃣ 测试故障分析功能:")
    try:
        test_query = "变压器油温过高故障分析"
        response = requests.post(
            'http://localhost:5002/api/v1/analyze',
            json={'query': test_query, 'model': 'deepseek-v3'},
            timeout=30
        )
        
        if response.status_code == 200:
            print("   ✅ 故障分析API响应正常")
            # 检查响应内容是否包含专业分析
            result = response.json()
            if 'result' in result:
                content = result['result']
                if len(content) > 100:
                    print("   ✅ 返回了详细分析内容")
                    # 检查是否包含专业术语
                    professional_terms = ['设备', '故障', '分析', '诊断', '处理']
                    found_terms = [term for term in professional_terms if term in content]
                    print(f"   专业术语检测: {len(found_terms)}/{len(professional_terms)} 个")
                else:
                    print("   ⚠️ 返回内容过短，可能不是专业分析")
            else:
                print("   ⚠️ 响应格式异常")
        else:
            print(f"   ❌ 故障分析API异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 故障分析测试失败: {e}")

if __name__ == "__main__":
    test_professional_system()
