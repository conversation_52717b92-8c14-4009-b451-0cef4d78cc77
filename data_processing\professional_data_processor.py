#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业级数据处理器
重构数据预处理管道，达到标准大模型的数据质量要求
"""

import os
import re
import json
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
import pandas as pd
from loguru import logger
import jieba
import numpy as np
from dataclasses import dataclass, asdict


@dataclass
class TechnicalDocument:
    """技术文档数据结构"""
    id: str
    title: str
    content: str
    equipment_type: str
    fault_type: str
    voltage_level: str
    location: str
    technical_parameters: Dict[str, Any]
    analysis_depth: str  # basic, intermediate, advanced, expert
    quality_score: float
    metadata: Dict[str, Any]
    created_at: str
    processed_at: str


@dataclass
class ProcessingMetrics:
    """处理质量指标"""
    total_documents: int
    processed_documents: int
    quality_passed: int
    technical_depth_score: float
    professional_terminology_score: float
    structural_completeness_score: float
    overall_quality_score: float


class ProfessionalDataProcessor:
    """专业级数据处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.output_dir = config.get("output_dir", "data/professional_processed")
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 专业电力系统知识图谱
        self.power_knowledge_graph = self._build_power_knowledge_graph()
        
        # 技术参数标准
        self.technical_standards = self._load_technical_standards()
        
        # 质量评估标准
        self.quality_standards = self._define_quality_standards()
        
        # 专业术语词典（扩展版）
        self.professional_terminology = self._build_professional_terminology()
        
        logger.info("专业级数据处理器初始化完成")
    
    def _build_power_knowledge_graph(self) -> Dict[str, Any]:
        """构建电力系统专业知识图谱"""
        return {
            "equipment_hierarchy": {
                "变压器": {
                    "subtypes": ["主变压器", "配电变压器", "仪用变压器", "调压变压器"],
                    "components": ["铁芯", "绕组", "套管", "冷却系统", "保护装置"],
                    "parameters": ["额定容量", "额定电压", "阻抗电压", "损耗", "绝缘等级"],
                    "common_faults": ["绕组故障", "铁芯故障", "套管故障", "冷却系统故障"]
                },
                "断路器": {
                    "subtypes": ["SF6断路器", "真空断路器", "油断路器", "空气断路器"],
                    "components": ["触头系统", "灭弧室", "操作机构", "支撑绝缘子"],
                    "parameters": ["额定电压", "额定电流", "开断电流", "操作时间", "机械寿命"],
                    "common_faults": ["拒动", "误动", "触头烧损", "操作机构故障", "绝缘故障"]
                },
                "保护系统": {
                    "subtypes": ["主保护", "后备保护", "辅助保护", "自动装置"],
                    "functions": ["差动保护", "距离保护", "过流保护", "接地保护"],
                    "parameters": ["动作时间", "灵敏度", "选择性", "可靠性"],
                    "common_issues": ["误动", "拒动", "定值不当", "配合不当"]
                }
            },
            "fault_causality": {
                "绝缘故障": {
                    "causes": ["绝缘老化", "过电压", "污闪", "受潮", "机械损伤"],
                    "symptoms": ["局部放电", "绝缘电阻下降", "介质损耗增加", "击穿"],
                    "consequences": ["设备损坏", "停电", "人身安全", "火灾风险"]
                },
                "机械故障": {
                    "causes": ["磨损", "疲劳", "腐蚀", "松动", "变形"],
                    "symptoms": ["异音", "振动", "温升", "动作异常"],
                    "consequences": ["功能失效", "可靠性下降", "维修成本增加"]
                }
            },
            "operational_relationships": {
                "电压等级": ["1000kV", "800kV", "500kV", "330kV", "220kV", "110kV", "35kV", "10kV", "6kV"],
                "系统运行方式": ["正常运行", "检修运行", "事故运行", "特殊运行"],
                "保护配合": ["主保护", "近后备保护", "远后备保护", "断路器失灵保护"]
            }
        }
    
    def _load_technical_standards(self) -> Dict[str, Any]:
        """加载技术标准"""
        return {
            "voltage_levels": {
                "1000kV": {"type": "特高压交流", "insulation_level": "1050/1425/1550"},
                "800kV": {"type": "特高压直流", "insulation_level": "800/1050"},
                "500kV": {"type": "超高压", "insulation_level": "525/630/750"},
                "220kV": {"type": "高压", "insulation_level": "245/325/460"},
                "110kV": {"type": "高压", "insulation_level": "126/185/230"},
                "35kV": {"type": "中压", "insulation_level": "40.5/75/95"},
                "10kV": {"type": "中压", "insulation_level": "12/28/42"}
            },
            "equipment_standards": {
                "变压器": {
                    "capacity_range": "10MVA-1000MVA",
                    "efficiency": ">99%",
                    "temperature_rise": "<65K",
                    "noise_level": "<65dB"
                },
                "断路器": {
                    "breaking_time": "<50ms",
                    "making_time": "<150ms",
                    "mechanical_life": ">10000次",
                    "electrical_life": ">100次额定开断"
                }
            },
            "protection_standards": {
                "差动保护": {"action_time": "<20ms", "sensitivity": "0.3-0.5In"},
                "距离保护": {"action_time": "I段<100ms, II段<500ms", "reach": "80%-85%线路长度"},
                "过流保护": {"action_time": "0.5-3s", "sensitivity": "1.2-1.5In"}
            }
        }
    
    def _define_quality_standards(self) -> Dict[str, Any]:
        """定义质量评估标准"""
        return {
            "content_quality": {
                "min_length": 500,  # 最小字符数
                "technical_term_density": 0.15,  # 专业术语密度
                "parameter_presence": 0.8,  # 技术参数存在比例
                "structural_completeness": 0.9  # 结构完整性
            },
            "technical_depth": {
                "basic": {"score_range": [0.3, 0.5], "requirements": ["基本现象描述", "简单原因分析"]},
                "intermediate": {"score_range": [0.5, 0.7], "requirements": ["技术参数", "分析过程", "处理方法"]},
                "advanced": {"score_range": [0.7, 0.85], "requirements": ["深度分析", "多因素考虑", "预防措施"]},
                "expert": {"score_range": [0.85, 1.0], "requirements": ["专家级分析", "系统性思考", "创新见解"]}
            },
            "professional_requirements": {
                "必须包含": ["设备型号", "技术参数", "故障现象", "分析过程", "处理方案"],
                "推荐包含": ["历史数据", "对比分析", "经验总结", "改进建议"],
                "质量指标": {
                    "专业术语准确性": 0.95,
                    "技术逻辑合理性": 0.90,
                    "实用性": 0.85,
                    "创新性": 0.70
                }
            }
        }
    
    def _build_professional_terminology(self) -> Dict[str, Any]:
        """构建专业术语词典（大幅扩展）"""
        return {
            "设备类型": {
                "primary": ["变压器", "断路器", "隔离开关", "电流互感器", "电压互感器", "避雷器", "电抗器", "电容器"],
                "secondary": ["保护装置", "测控装置", "通信设备", "直流系统", "交流系统", "监控系统"],
                "components": ["套管", "绕组", "铁芯", "触头", "操作机构", "绝缘子", "冷却器", "油箱"]
            },
            "故障类型": {
                "electrical": ["短路", "接地", "断线", "过载", "过压", "欠压", "谐波", "闪络"],
                "mechanical": ["卡涩", "松动", "磨损", "变形", "断裂", "腐蚀", "振动", "异音"],
                "thermal": ["过热", "温升", "冷却不良", "热老化", "热膨胀", "热应力"],
                "insulation": ["绝缘击穿", "绝缘老化", "局部放电", "受潮", "污闪", "绝缘电阻下降"]
            },
            "技术参数": {
                "electrical": ["电压", "电流", "功率", "频率", "阻抗", "电阻", "电抗", "电容"],
                "mechanical": ["压力", "温度", "振动", "位移", "力矩", "速度", "加速度"],
                "insulation": ["绝缘电阻", "介质损耗", "局部放电", "耐压", "泄漏电流"],
                "protection": ["动作时间", "返回时间", "灵敏度", "选择性", "可靠性", "速动性"]
            },
            "分析方法": {
                "diagnostic": ["故障录波分析", "保护动作分析", "设备状态分析", "运行参数分析"],
                "testing": ["绝缘试验", "机械特性试验", "保护定值校验", "设备巡检"],
                "monitoring": ["在线监测", "状态评估", "趋势分析", "预警分析"]
            }
        }
    
    def process_document_batch(self, documents: List[Dict[str, Any]]) -> Tuple[List[TechnicalDocument], ProcessingMetrics]:
        """批量处理文档，提升到专业级质量"""
        try:
            logger.info(f"开始专业级批量处理 {len(documents)} 个文档")
            
            processed_docs = []
            metrics = ProcessingMetrics(
                total_documents=len(documents),
                processed_documents=0,
                quality_passed=0,
                technical_depth_score=0.0,
                professional_terminology_score=0.0,
                structural_completeness_score=0.0,
                overall_quality_score=0.0
            )
            
            for doc in documents:
                processed_doc = self._process_single_document(doc)
                if processed_doc:
                    processed_docs.append(processed_doc)
                    metrics.processed_documents += 1
                    
                    if processed_doc.quality_score >= 0.7:  # 高质量标准
                        metrics.quality_passed += 1
            
            # 计算整体指标
            if processed_docs:
                metrics.technical_depth_score = np.mean([doc.quality_score for doc in processed_docs])
                metrics.professional_terminology_score = self._calculate_terminology_score(processed_docs)
                metrics.structural_completeness_score = self._calculate_structure_score(processed_docs)
                metrics.overall_quality_score = (
                    metrics.technical_depth_score * 0.4 +
                    metrics.professional_terminology_score * 0.3 +
                    metrics.structural_completeness_score * 0.3
                )
            else:
                metrics.technical_depth_score = 0.0
                metrics.professional_terminology_score = 0.0
                metrics.structural_completeness_score = 0.0
                metrics.overall_quality_score = 0.0
            
            logger.info(f"专业级处理完成: {metrics.processed_documents}/{metrics.total_documents} 文档")
            logger.info(f"质量通过率: {metrics.quality_passed/metrics.processed_documents*100:.1f}%")
            
            return processed_docs, metrics
            
        except Exception as e:
            logger.error(f"批量处理失败: {str(e)}")
            return [], metrics
    
    def _process_single_document(self, doc: Dict[str, Any]) -> Optional[TechnicalDocument]:
        """处理单个文档，提升到专业级质量"""
        try:
            # 提取基础信息
            content = doc.get('content', '')
            title = doc.get('title', '')
            
            if not content or len(content.strip()) < 100:
                return None
            
            # 专业化内容增强
            enhanced_content = self._enhance_content_professionally(content)
            
            # 提取技术参数
            technical_params = self._extract_technical_parameters(enhanced_content)
            
            # 分析技术深度
            analysis_depth = self._analyze_technical_depth(enhanced_content)
            
            # 质量评分
            quality_score = self._calculate_quality_score(enhanced_content, technical_params, analysis_depth)
            
            # 构建专业文档
            processed_doc = TechnicalDocument(
                id=str(uuid.uuid4()),
                title=self._enhance_title(title),
                content=enhanced_content,
                equipment_type=self._identify_equipment_type(enhanced_content),
                fault_type=self._identify_fault_type(enhanced_content),
                voltage_level=self._extract_voltage_level(enhanced_content),
                location=doc.get('metadata', {}).get('location', '白银市'),
                technical_parameters=technical_params,
                analysis_depth=analysis_depth,
                quality_score=quality_score,
                metadata=self._build_enhanced_metadata(doc, enhanced_content),
                created_at=datetime.now().isoformat(),
                processed_at=datetime.now().isoformat()
            )
            
            return processed_doc
            
        except Exception as e:
            logger.error(f"单文档处理失败: {str(e)}")
            return None

    def _enhance_content_professionally(self, content: str) -> str:
        """专业化内容增强"""
        try:
            # 1. 标准化专业术语
            enhanced_content = self._standardize_professional_terms(content)

            # 2. 补充技术细节
            enhanced_content = self._add_technical_details(enhanced_content)

            # 3. 结构化组织
            enhanced_content = self._restructure_content(enhanced_content)

            # 4. 添加专业分析
            enhanced_content = self._add_professional_analysis(enhanced_content)

            return enhanced_content

        except Exception as e:
            logger.error(f"内容专业化增强失败: {str(e)}")
            return content

    def _standardize_professional_terms(self, content: str) -> str:
        """标准化专业术语"""
        try:
            enhanced_content = content

            # 遍历专业术语词典进行标准化
            for category, terms in self.professional_terminology.items():
                if isinstance(terms, dict):
                    for subcategory, term_list in terms.items():
                        for term in term_list:
                            # 使用正则表达式进行精确匹配和替换
                            pattern = rf'\b{re.escape(term)}\b'
                            if re.search(pattern, enhanced_content, re.IGNORECASE):
                                enhanced_content = re.sub(pattern, term, enhanced_content, flags=re.IGNORECASE)

            return enhanced_content

        except Exception as e:
            logger.error(f"专业术语标准化失败: {str(e)}")
            return content

    def _add_technical_details(self, content: str) -> str:
        """添加技术细节"""
        try:
            # 识别设备类型并添加相应技术参数
            equipment_type = self._identify_equipment_type(content)
            voltage_level = self._extract_voltage_level(content)

            technical_details = []

            # 根据设备类型添加标准技术参数
            if equipment_type in self.power_knowledge_graph["equipment_hierarchy"]:
                equipment_info = self.power_knowledge_graph["equipment_hierarchy"][equipment_type]

                # 添加技术参数说明
                if "parameters" in equipment_info:
                    params = equipment_info["parameters"][:3]  # 取前3个主要参数
                    technical_details.append(f"\n**主要技术参数**: {', '.join(params)}")

                # 添加组件信息
                if "components" in equipment_info:
                    components = equipment_info["components"][:4]  # 取前4个主要组件
                    technical_details.append(f"**主要组件**: {', '.join(components)}")

            # 根据电压等级添加绝缘水平信息
            if voltage_level and voltage_level in self.technical_standards["voltage_levels"]:
                insulation_info = self.technical_standards["voltage_levels"][voltage_level]
                technical_details.append(f"**绝缘水平**: {insulation_info['insulation_level']}")

            # 将技术细节插入到内容中
            if technical_details:
                enhanced_content = content + "\n\n" + "\n".join(technical_details)
                return enhanced_content

            return content

        except Exception as e:
            logger.error(f"添加技术细节失败: {str(e)}")
            return content

    def _restructure_content(self, content: str) -> str:
        """结构化重组内容"""
        try:
            # 检查是否已经有良好的结构
            if self._has_good_structure(content):
                return content

            # 提取关键信息
            equipment_info = self._extract_equipment_info(content)
            fault_info = self._extract_fault_info(content)
            analysis_info = self._extract_analysis_info(content)
            solution_info = self._extract_solution_info(content)

            # 重新组织结构
            structured_content = []

            # 1. 设备基本信息
            if equipment_info:
                structured_content.append("## 设备信息")
                structured_content.append(equipment_info)

            # 2. 故障现象
            if fault_info:
                structured_content.append("\n## 故障现象")
                structured_content.append(fault_info)

            # 3. 技术分析
            if analysis_info:
                structured_content.append("\n## 技术分析")
                structured_content.append(analysis_info)
            else:
                # 如果没有分析，基于故障类型生成基础分析
                structured_content.append("\n## 技术分析")
                structured_content.append(self._generate_basic_analysis(content))

            # 4. 处理方案
            if solution_info:
                structured_content.append("\n## 处理方案")
                structured_content.append(solution_info)

            # 5. 预防措施
            structured_content.append("\n## 预防措施")
            structured_content.append(self._generate_prevention_measures(content))

            return "\n".join(structured_content)

        except Exception as e:
            logger.error(f"内容结构化失败: {str(e)}")
            return content

    def _add_professional_analysis(self, content: str) -> str:
        """添加专业分析"""
        try:
            # 识别故障类型
            fault_type = self._identify_fault_type(content)
            equipment_type = self._identify_equipment_type(content)

            professional_analysis = []

            # 基于故障类型添加专业分析
            if fault_type in self.power_knowledge_graph["fault_causality"]:
                fault_info = self.power_knowledge_graph["fault_causality"][fault_type]

                # 添加原因分析
                if "causes" in fault_info:
                    causes = fault_info["causes"][:3]
                    professional_analysis.append(f"\n**可能原因分析**: {', '.join(causes)}")

                # 添加症状分析
                if "symptoms" in fault_info:
                    symptoms = fault_info["symptoms"][:3]
                    professional_analysis.append(f"**典型症状**: {', '.join(symptoms)}")

                # 添加后果分析
                if "consequences" in fault_info:
                    consequences = fault_info["consequences"][:2]
                    professional_analysis.append(f"**潜在后果**: {', '.join(consequences)}")

            # 基于设备类型添加专业建议
            if equipment_type in self.power_knowledge_graph["equipment_hierarchy"]:
                equipment_info = self.power_knowledge_graph["equipment_hierarchy"][equipment_type]
                if "common_faults" in equipment_info:
                    common_faults = equipment_info["common_faults"][:3]
                    professional_analysis.append(f"**该设备常见故障**: {', '.join(common_faults)}")

            # 将专业分析添加到内容中
            if professional_analysis:
                return content + "\n\n" + "\n".join(professional_analysis)

            return content

        except Exception as e:
            logger.error(f"添加专业分析失败: {str(e)}")
            return content

    def _extract_technical_parameters(self, content: str) -> Dict[str, Any]:
        """提取技术参数"""
        try:
            parameters = {}

            # 电压参数
            voltage_pattern = r'(\d+(?:\.\d+)?)\s*[kKmM]?[vV]'
            voltage_matches = re.findall(voltage_pattern, content)
            if voltage_matches:
                parameters['voltage'] = [float(v) for v in voltage_matches[:3]]

            # 电流参数
            current_pattern = r'(\d+(?:\.\d+)?)\s*[aA]'
            current_matches = re.findall(current_pattern, content)
            if current_matches:
                parameters['current'] = [float(i) for i in current_matches[:3]]

            # 功率参数
            power_pattern = r'(\d+(?:\.\d+)?)\s*[kKmM]?[wW]'
            power_matches = re.findall(power_pattern, content)
            if power_matches:
                parameters['power'] = [float(p) for p in power_matches[:3]]

            # 温度参数
            temp_pattern = r'(\d+(?:\.\d+)?)\s*[℃°]'
            temp_matches = re.findall(temp_pattern, content)
            if temp_matches:
                parameters['temperature'] = [float(t) for t in temp_matches[:3]]

            # 时间参数
            time_pattern = r'(\d+(?:\.\d+)?)\s*[msMS秒分时]'
            time_matches = re.findall(time_pattern, content)
            if time_matches:
                parameters['time'] = [float(t) for t in time_matches[:3]]

            return parameters

        except Exception as e:
            logger.error(f"技术参数提取失败: {str(e)}")
            return {}

    def _analyze_technical_depth(self, content: str) -> str:
        """分析技术深度"""
        try:
            depth_indicators = {
                'basic': ['故障', '现象', '检查', '发现'],
                'intermediate': ['分析', '原因', '参数', '测试', '检修'],
                'advanced': ['机理', '理论', '计算', '仿真', '优化', '改进'],
                'expert': ['创新', '研究', '算法', '模型', '预测', '智能']
            }

            content_lower = content.lower()
            depth_scores = {}

            for depth, indicators in depth_indicators.items():
                score = sum(1 for indicator in indicators if indicator in content_lower)
                depth_scores[depth] = score / len(indicators)

            # 确定技术深度等级
            max_score = max(depth_scores.values())
            for depth, score in depth_scores.items():
                if score == max_score and score > 0.3:
                    return depth

            return 'basic'

        except Exception as e:
            logger.error(f"技术深度分析失败: {str(e)}")
            return 'basic'

    def _calculate_quality_score(self, content: str, technical_params: Dict[str, Any], analysis_depth: str) -> float:
        """计算质量评分"""
        try:
            scores = []

            # 1. 内容长度评分 (0-0.2)
            length_score = min(len(content) / 2000, 1.0) * 0.2
            scores.append(length_score)

            # 2. 专业术语密度评分 (0-0.25)
            term_density = self._calculate_term_density(content)
            term_score = min(term_density / 0.15, 1.0) * 0.25
            scores.append(term_score)

            # 3. 技术参数丰富度评分 (0-0.2)
            param_score = min(len(technical_params) / 5, 1.0) * 0.2
            scores.append(param_score)

            # 4. 技术深度评分 (0-0.25)
            depth_scores = {'basic': 0.3, 'intermediate': 0.6, 'advanced': 0.8, 'expert': 1.0}
            depth_score = depth_scores.get(analysis_depth, 0.3) * 0.25
            scores.append(depth_score)

            # 5. 结构完整性评分 (0-0.1)
            structure_score = self._evaluate_structure_completeness(content) * 0.1
            scores.append(structure_score)

            total_score = sum(scores)
            return min(total_score, 1.0)

        except Exception as e:
            logger.error(f"质量评分计算失败: {str(e)}")
            return 0.5

    def _calculate_term_density(self, content: str) -> float:
        """计算专业术语密度"""
        try:
            if not content:
                return 0.0

            # 分词
            words = jieba.lcut(content)
            total_words = len(words)

            if total_words == 0:
                return 0.0

            # 统计专业术语
            professional_terms = 0
            for category, terms in self.professional_terminology.items():
                if isinstance(terms, dict):
                    for subcategory, term_list in terms.items():
                        for term in term_list:
                            professional_terms += content.count(term)
                elif isinstance(terms, list):
                    for term in terms:
                        professional_terms += content.count(term)

            return professional_terms / total_words

        except Exception as e:
            logger.error(f"术语密度计算失败: {str(e)}")
            return 0.0

    def _evaluate_structure_completeness(self, content: str) -> float:
        """评估结构完整性"""
        try:
            required_sections = ['设备', '故障', '分析', '处理', '措施']
            present_sections = 0

            content_lower = content.lower()
            for section in required_sections:
                if section in content_lower:
                    present_sections += 1

            return present_sections / len(required_sections)

        except Exception as e:
            logger.error(f"结构完整性评估失败: {str(e)}")
            return 0.5

    def _identify_equipment_type(self, content: str) -> str:
        """识别设备类型"""
        try:
            content_lower = content.lower()

            # 按优先级检查设备类型
            equipment_priorities = [
                ('变压器', ['变压器', '主变', '配变', '变电器']),
                ('断路器', ['断路器', '开关', 'sf6', '真空开关']),
                ('隔离开关', ['隔离开关', '刀闸', '隔离刀闸']),
                ('电流互感器', ['电流互感器', 'ct', '电流表']),
                ('电压互感器', ['电压互感器', 'pt', '电压表']),
                ('避雷器', ['避雷器', '氧化锌避雷器', '避雷针']),
                ('保护装置', ['保护装置', '继电保护', '保护系统'])
            ]

            for equipment_type, keywords in equipment_priorities:
                for keyword in keywords:
                    if keyword in content_lower:
                        return equipment_type

            return '通用设备'

        except Exception as e:
            logger.error(f"设备类型识别失败: {str(e)}")
            return '未知设备'

    def _identify_fault_type(self, content: str) -> str:
        """识别故障类型"""
        try:
            content_lower = content.lower()

            # 按优先级检查故障类型
            fault_priorities = [
                ('绝缘故障', ['绝缘', '击穿', '闪络', '放电', '受潮']),
                ('机械故障', ['卡涩', '松动', '磨损', '变形', '断裂', '操作']),
                ('电气故障', ['短路', '接地', '过载', '过压', '欠压']),
                ('保护故障', ['误动', '拒动', '保护', '跳闸', '动作']),
                ('通信故障', ['通信', '信号', '数据', '网络', '传输']),
                ('控制故障', ['控制', '操作', '指令', '遥控', '就地'])
            ]

            for fault_type, keywords in fault_priorities:
                for keyword in keywords:
                    if keyword in content_lower:
                        return fault_type

            return '一般故障'

        except Exception as e:
            logger.error(f"故障类型识别失败: {str(e)}")
            return '未知故障'

    def _extract_voltage_level(self, content: str) -> str:
        """提取电压等级"""
        try:
            # 电压等级模式匹配
            voltage_patterns = [
                (r'1000\s*kv', '1000kV'),
                (r'800\s*kv', '800kV'),
                (r'500\s*kv', '500kV'),
                (r'330\s*kv', '330kV'),
                (r'220\s*kv', '220kV'),
                (r'110\s*kv', '110kV'),
                (r'35\s*kv', '35kV'),
                (r'10\s*kv', '10kV'),
                (r'6\s*kv', '6kV')
            ]

            content_lower = content.lower()
            for pattern, voltage_level in voltage_patterns:
                if re.search(pattern, content_lower):
                    return voltage_level

            return '未知电压等级'

        except Exception as e:
            logger.error(f"电压等级提取失败: {str(e)}")
            return '未知电压等级'

    def _enhance_title(self, title: str) -> str:
        """增强标题"""
        try:
            if not title or len(title.strip()) < 5:
                return "电力设备故障分析报告"

            # 确保标题包含关键信息
            enhanced_title = title.strip()

            # 如果标题太简单，进行增强
            if len(enhanced_title) < 15:
                enhanced_title = f"白银电力系统{enhanced_title}专业分析报告"

            return enhanced_title

        except Exception as e:
            logger.error(f"标题增强失败: {str(e)}")
            return title or "故障分析报告"

    def _build_enhanced_metadata(self, original_doc: Dict[str, Any], enhanced_content: str) -> Dict[str, Any]:
        """构建增强元数据"""
        try:
            metadata = original_doc.get('metadata', {}).copy()

            # 添加处理信息
            metadata.update({
                'processing_version': '2.0_professional',
                'content_length': len(enhanced_content),
                'professional_terms_count': self._count_professional_terms(enhanced_content),
                'technical_parameters_count': len(self._extract_technical_parameters(enhanced_content)),
                'structure_score': self._evaluate_structure_completeness(enhanced_content),
                'enhancement_applied': True,
                'processing_timestamp': datetime.now().isoformat()
            })

            return metadata

        except Exception as e:
            logger.error(f"元数据构建失败: {str(e)}")
            return original_doc.get('metadata', {})

    def _count_professional_terms(self, content: str) -> int:
        """统计专业术语数量"""
        try:
            count = 0
            for category, terms in self.professional_terminology.items():
                if isinstance(terms, dict):
                    for subcategory, term_list in terms.items():
                        for term in term_list:
                            count += content.count(term)
                elif isinstance(terms, list):
                    for term in terms:
                        count += content.count(term)
            return count

        except Exception as e:
            logger.error(f"专业术语统计失败: {str(e)}")
            return 0

    def _calculate_terminology_score(self, processed_docs: List[TechnicalDocument]) -> float:
        """计算专业术语评分"""
        try:
            if not processed_docs:
                return 0.0

            total_score = 0.0
            for doc in processed_docs:
                term_count = self._count_professional_terms(doc.content)
                content_length = len(doc.content)
                if content_length > 0:
                    density = term_count / content_length
                    total_score += min(density * 100, 1.0)  # 归一化到0-1

            return total_score / len(processed_docs)

        except Exception as e:
            logger.error(f"术语评分计算失败: {str(e)}")
            return 0.0

    def _calculate_structure_score(self, processed_docs: List[TechnicalDocument]) -> float:
        """计算结构评分"""
        try:
            if not processed_docs:
                return 0.0

            total_score = 0.0
            for doc in processed_docs:
                structure_score = self._evaluate_structure_completeness(doc.content)
                total_score += structure_score

            return total_score / len(processed_docs)

        except Exception as e:
            logger.error(f"结构评分计算失败: {str(e)}")
            return 0.0

    # 添加缺失的辅助方法
    def _has_good_structure(self, content: str) -> bool:
        """检查是否已有良好结构"""
        structure_indicators = ['##', '**', '【', '】', '1.', '2.', '3.']
        return sum(1 for indicator in structure_indicators if indicator in content) >= 3

    def _extract_equipment_info(self, content: str) -> str:
        """提取设备信息"""
        equipment_patterns = [
            r'(\d+kV.*?变压器)',
            r'(SF6.*?断路器)',
            r'(\w+变电站)',
            r'(型号.*?\w+)',
            r'(容量.*?\d+MVA)'
        ]

        equipment_info = []
        for pattern in equipment_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            equipment_info.extend(matches[:2])  # 限制数量

        return '\n'.join(equipment_info) if equipment_info else "设备信息待补充"

    def _extract_fault_info(self, content: str) -> str:
        """提取故障信息"""
        fault_keywords = ['故障', '异常', '缺陷', '损坏', '失效', '渗油', '过热', '跳闸']
        fault_sentences = []

        sentences = content.split('。')
        for sentence in sentences:
            if any(keyword in sentence for keyword in fault_keywords):
                fault_sentences.append(sentence.strip())
                if len(fault_sentences) >= 3:
                    break

        return '。'.join(fault_sentences) if fault_sentences else "故障现象待详细描述"

    def _extract_analysis_info(self, content: str) -> str:
        """提取分析信息"""
        analysis_keywords = ['分析', '原因', '机理', '检查', '测试', '诊断']
        analysis_sentences = []

        sentences = content.split('。')
        for sentence in sentences:
            if any(keyword in sentence for keyword in analysis_keywords):
                analysis_sentences.append(sentence.strip())
                if len(analysis_sentences) >= 3:
                    break

        return '。'.join(analysis_sentences) if analysis_sentences else ""

    def _extract_solution_info(self, content: str) -> str:
        """提取解决方案信息"""
        solution_keywords = ['处理', '解决', '修复', '更换', '调整', '措施']
        solution_sentences = []

        sentences = content.split('。')
        for sentence in sentences:
            if any(keyword in sentence for keyword in solution_keywords):
                solution_sentences.append(sentence.strip())
                if len(solution_sentences) >= 3:
                    break

        return '。'.join(solution_sentences) if solution_sentences else ""

    def _generate_basic_analysis(self, content: str) -> str:
        """生成基础分析"""
        equipment_type = self._identify_equipment_type(content)
        fault_type = self._identify_fault_type(content)

        return f"基于{equipment_type}的{fault_type}，需要进行详细的技术分析和故障机理研究。"

    def _generate_prevention_measures(self, content: str) -> str:
        """生成预防措施"""
        equipment_type = self._identify_equipment_type(content)

        general_measures = [
            f"加强{equipment_type}的定期巡视检查",
            "建立设备状态监测机制",
            "完善设备维护保养制度",
            "提高运维人员技术水平"
        ]

        return '\n'.join(f"- {measure}" for measure in general_measures)
