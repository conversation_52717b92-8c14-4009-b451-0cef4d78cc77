{"case_embeddings": {"collection_id": "CASE_EMB_001", "collection_name": "故障案例向量嵌入", "created_date": "2024-01-16T14:00:00Z", "model_info": {"model_name": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2", "embedding_dimension": 384, "max_sequence_length": 512}, "total_cases": 75, "case_categories": ["transformer_fault", "breaker_fault", "protection_malfunction", "insulation_failure", "mechanical_failure"]}, "case_studies": [{"case_id": "CASE_001", "case_title": "110kV变压器差动保护误动案例", "case_category": "protection_malfunction", "equipment_involved": ["TR001", "PROT_TR001_DIFF"], "incident_date": "2023-08-15T14:30:00Z", "case_summary": "110kV变压器在正常运行时差动保护突然动作，导致变压器跳闸。经检查发现是CT二次回路接触不良导致的保护误动。", "detailed_description": "当日14:30，110kV变压器差动保护动作，高压侧断路器跳闸。现场检查变压器外观正常，无异常声响和气味。通过保护装置录波分析发现，A相电流互感器二次电流突然减小，导致差动保护误动。进一步检查发现A相CT二次端子接触不良，清理端子后恢复正常。", "root_cause": "电流互感器二次端子接触不良", "corrective_actions": ["清理CT二次端子", "紧固端子螺栓", "检查其他CT端子", "加强巡视检查", "完善预防性维护"], "lessons_learned": ["定期检查CT二次回路连接", "重视端子接触状态", "建立CT二次回路维护制度", "加强保护装置录波分析"], "embedding_vector": [0.0678, -0.0912, 0.0345, -0.0567, 0.0891, 0.0234, -0.0678, 0.0912, -0.0345, 0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, -0.0567, 0.0891, -0.0234, 0.0678, -0.0912, 0.0345, 0.0567, -0.0891, 0.0234, -0.0678, 0.0912, -0.0345, -0.0567, 0.0891, 0.0234, 0.0678, -0.0912, 0.0345, 0.0567, 0.0891, -0.0234, -0.0678, 0.0912, 0.0345, -0.0567, -0.0891, 0.0234, 0.0678, -0.0912, -0.0345, 0.0567, 0.0891, -0.0234, 0.0678, 0.0912, 0.0345, 0.0567, -0.0891, 0.0234, -0.0678, -0.0912, 0.0345, -0.0567, 0.0891, -0.0234, 0.0678, 0.0912, -0.0345, 0.0567, 0.0891, 0.0234, -0.0678, -0.0912, 0.0345, -0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, -0.0234, -0.0678, -0.0912, -0.0345, -0.0567, -0.0891, 0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, -0.0345, -0.0567, -0.0891, -0.0234, -0.0678, 0.0912, 0.0345, 0.0567, 0.0891, 0.0234, 0.0678, 0.0912, 0.0345, -0.0567, -0.0891, -0.0234, -0.0678, -0.0912, -0.0345, 0.0567, 0.0891, 0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, -0.0234, 0.0678, -0.0912, -0.0345, -0.0567, -0.0891, 0.0234, -0.0678, 0.0912, 0.0345, 0.0567, 0.0891, -0.0234, 0.0678, -0.0912, 0.0345, -0.0567, -0.0891, -0.0234, -0.0678, 0.0912, -0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, 0.0345, 0.0567, -0.0891, -0.0234, 0.0678, 0.0912, -0.0345, -0.0567, 0.0891, 0.0234, -0.0678, -0.0912, 0.0345, 0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, -0.0567, 0.0891, 0.0234, -0.0678, -0.0912, -0.0345, 0.0567, -0.0891, -0.0234, -0.0678, 0.0912, 0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, -0.0345, -0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, -0.0234, -0.0678, -0.0912, -0.0345, -0.0567, -0.0891, 0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, 0.0345, -0.0567, -0.0891, -0.0234, -0.0678, 0.0912, -0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, 0.0345, 0.0567, -0.0891, -0.0234, 0.0678, 0.0912, -0.0345, -0.0567, 0.0891, 0.0234, -0.0678, -0.0912, 0.0345, 0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, -0.0567, 0.0891, 0.0234, -0.0678, -0.0912, -0.0345, 0.0567, -0.0891, -0.0234, -0.0678, 0.0912, 0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, -0.0345, -0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, -0.0234, -0.0678, -0.0912, -0.0345, -0.0567, -0.0891, 0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, 0.0345, -0.0567, -0.0891, -0.0234, -0.0678, 0.0912, -0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, 0.0345, 0.0567, -0.0891, -0.0234, 0.0678, 0.0912, -0.0345, -0.0567, 0.0891, 0.0234, -0.0678, -0.0912, 0.0345, 0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, -0.0567, 0.0891, 0.0234, -0.0678, -0.0912, -0.0345, 0.0567, -0.0891, -0.0234, -0.0678, 0.0912, 0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, -0.0345, -0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, -0.0234, -0.0678, -0.0912, -0.0345, -0.0567, -0.0891, 0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, 0.0345, -0.0567, -0.0891, -0.0234, -0.0678, 0.0912, -0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, 0.0345, 0.0567, -0.0891, -0.0234, 0.0678, 0.0912, -0.0345, -0.0567, 0.0891, 0.0234, -0.0678, -0.0912, 0.0345, 0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, -0.0567, 0.0891, 0.0234, -0.0678, -0.0912, -0.0345, 0.0567, -0.0891, -0.0234, -0.0678, 0.0912, 0.0345, 0.0567, 0.0891, 0.0234, 0.0678, -0.0912, -0.0345, -0.0567, -0.0891, -0.0234, 0.0678, 0.0912, 0.0345, 0.0567, 0.0891, -0.0234, -0.0678, -0.0912, -0.0345, -0.0567, -0.0891, 0.0234], "similar_cases": ["CASE_015", "CASE_028", "CASE_042"], "similarity_scores": {"CASE_015": 0.87, "CASE_028": 0.82, "CASE_042": 0.79}, "metadata": {"processing_date": "2024-01-16T14:00:00Z", "case_complexity": "medium", "resolution_time": "4小时", "economic_impact": "中等", "confidence": 0.94}}, {"case_id": "CASE_002", "case_title": "断路器SF6气体泄漏导致拒动故障", "case_category": "breaker_fault", "equipment_involved": ["CB002"], "incident_date": "2023-09-22T10:15:00Z", "case_summary": "110kV出线断路器因SF6气体压力低导致拒动故障，线路故障时断路器未能正常分闸，后备保护动作切除故障。", "detailed_description": "线路发生接地故障时，线路保护动作发出跳闸指令，但断路器未能分闸。检查发现断路器SF6气体压力仅为0.35MPa，低于闭锁压力0.4MPa。进一步检查发现气体密封圈老化导致缓慢泄漏。更换密封圈并补充气体后恢复正常。", "root_cause": "SF6气体密封圈老化导致气体泄漏", "corrective_actions": ["更换老化密封圈", "补充SF6气体至额定压力", "检查其他断路器气体状态", "建立气体压力监测制度", "加强预防性维护"], "lessons_learned": ["定期检查SF6气体压力", "重视密封系统维护", "建立气体泄漏监测系统", "制定气体补充应急预案"], "embedding_vector": [-0.0456, 0.0789, -0.0123, 0.0345, -0.0678, 0.0912, 0.0456, -0.0789, 0.0123, -0.0345, 0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, -0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, 0.0678, -0.0912, 0.0456, -0.0789, 0.0123, 0.0345, -0.0678, -0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, 0.0912, 0.0456, -0.0789, -0.0123, 0.0345, 0.0678, -0.0912, -0.0456, 0.0789, 0.0123, -0.0345, -0.0678, 0.0912, -0.0456, -0.0789, -0.0123, -0.0345, 0.0678, -0.0912, 0.0456, 0.0789, -0.0123, 0.0345, -0.0678, 0.0912, -0.0456, -0.0789, 0.0123, -0.0345, -0.0678, -0.0912, 0.0456, 0.0789, -0.0123, 0.0345, 0.0678, 0.0912, -0.0456, -0.0789, -0.0123, -0.0345, -0.0678, 0.0912, 0.0456, 0.0789, 0.0123, 0.0345, 0.0678, -0.0912, -0.0456, -0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, 0.0789, 0.0123, 0.0345, 0.0678, 0.0912, 0.0456, -0.0789, -0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, 0.0678, 0.0912, 0.0456, 0.0789, 0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, -0.0345, -0.0678, 0.0912, -0.0456, 0.0789, 0.0123, 0.0345, 0.0678, -0.0912, 0.0456, -0.0789, -0.0123, -0.0345, 0.0678, 0.0912, 0.0456, 0.0789, -0.0123, 0.0345, -0.0678, -0.0912, -0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, 0.0456, -0.0789, 0.0123, 0.0345, 0.0678, -0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, 0.0912, 0.0456, -0.0789, -0.0123, 0.0345, 0.0678, -0.0912, -0.0456, -0.0789, 0.0123, -0.0345, 0.0678, 0.0912, 0.0456, 0.0789, -0.0123, 0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, 0.0678, 0.0912, 0.0456, 0.0789, 0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, -0.0345, -0.0678, 0.0912, -0.0456, 0.0789, 0.0123, 0.0345, 0.0678, -0.0912, 0.0456, -0.0789, -0.0123, -0.0345, 0.0678, 0.0912, 0.0456, 0.0789, -0.0123, 0.0345, -0.0678, -0.0912, -0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, 0.0456, -0.0789, 0.0123, 0.0345, 0.0678, -0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, 0.0912, 0.0456, -0.0789, -0.0123, 0.0345, 0.0678, -0.0912, -0.0456, -0.0789, 0.0123, -0.0345, 0.0678, 0.0912, 0.0456, 0.0789, -0.0123, 0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, 0.0678, 0.0912, 0.0456, 0.0789, 0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, -0.0345, -0.0678, 0.0912, -0.0456, 0.0789, 0.0123, 0.0345, 0.0678, -0.0912, 0.0456, -0.0789, -0.0123, -0.0345, 0.0678, 0.0912, 0.0456, 0.0789, -0.0123, 0.0345, -0.0678, -0.0912, -0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, 0.0456, -0.0789, 0.0123, 0.0345, 0.0678, -0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, 0.0912, 0.0456, -0.0789, -0.0123, 0.0345, 0.0678, -0.0912, -0.0456, -0.0789, 0.0123, -0.0345, 0.0678, 0.0912, 0.0456, 0.0789, -0.0123, 0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, 0.0345, 0.0678, 0.0912, 0.0456, 0.0789, 0.0123, -0.0345, -0.0678, -0.0912, -0.0456, -0.0789, -0.0123, -0.0345, -0.0678, 0.0912, -0.0456, 0.0789, 0.0123, 0.0345, 0.0678, -0.0912, 0.0456, -0.0789, -0.0123, -0.0345, 0.0678, 0.0912, 0.0456, 0.0789, -0.0123, 0.0345, -0.0678, -0.0912, -0.0456, -0.0789, 0.0123, 0.0345, 0.0678, 0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, -0.0912, 0.0456, -0.0789, 0.0123, 0.0345, 0.0678, -0.0912, -0.0456, 0.0789, -0.0123, -0.0345, -0.0678, 0.0912, 0.0456, -0.0789, -0.0123, 0.0345, 0.0678, -0.0912, -0.0456, -0.0789, 0.0123, -0.0345, 0.0678, 0.0912], "similar_cases": ["CASE_008", "CASE_019", "CASE_035"], "similarity_scores": {"CASE_008": 0.91, "CASE_019": 0.85, "CASE_035": 0.78}, "metadata": {"processing_date": "2024-01-16T14:05:00Z", "case_complexity": "high", "resolution_time": "8小时", "economic_impact": "高", "confidence": 0.92}}], "case_clustering": {"algorithm": "Hierarchical", "n_clusters": 8, "clusters": [{"cluster_id": 0, "cluster_name": "变压器保护故障", "case_count": 12, "representative_cases": ["CASE_001", "CASE_015", "CASE_028"]}, {"cluster_id": 1, "cluster_name": "断路器机械故障", "case_count": 15, "representative_cases": ["CASE_002", "CASE_008", "CASE_019"]}]}, "case_retrieval_metrics": {"average_retrieval_time": "3.2ms", "precision_at_5": 0.89, "recall_at_10": 0.85, "case_relevance_score": 0.87}, "quality_metrics": {"case_completeness": 0.92, "semantic_coherence": 0.88, "similarity_accuracy": 0.86, "knowledge_extraction": 0.9}}