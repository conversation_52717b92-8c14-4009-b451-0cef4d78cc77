"""
知识库管理模块

统一管理文档、图像等知识资源
"""

import os
import json
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from loguru import logger

try:
    from data_processing.text_processor import TextProcessor
    from data_processing.image_processor import ImageProcessor
    from data_processing.ocr_processor import OCRProcessor
except ImportError as e:
    logger.warning(f"数据处理模块导入失败: {e}")
    TextProcessor = None
    ImageProcessor = None
    OCRProcessor = None


class KnowledgeBase:
    """知识库管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.text_path = config.get("text_path", "./knowledge_base/text")
        self.images_path = config.get("images_path", "./knowledge_base/images")
        self.mappings_path = config.get("mappings_path", "./knowledge_base/mappings")
        
        # 初始化处理器
        if TextProcessor:
            self.text_processor = TextProcessor(config.get("data_processing", {}))
        else:
            self.text_processor = None
            
        if ImageProcessor:
            self.image_processor = ImageProcessor(config.get("data_processing", {}).get("image", {}))
        else:
            self.image_processor = None
            
        if OCRProcessor:
            self.ocr_processor = OCRProcessor(config.get("data_processing", {}).get("ocr", {}))
        else:
            self.ocr_processor = None
        
        # 确保目录存在
        self._ensure_directories()
        
        # 加载现有知识库
        self._load_knowledge_base()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        try:
            for path in [self.text_path, self.images_path, self.mappings_path]:
                os.makedirs(path, exist_ok=True)
            logger.info("知识库目录结构检查完成")
        except Exception as e:
            logger.error(f"创建知识库目录失败: {e}")
    
    def _load_knowledge_base(self):
        """加载现有知识库"""
        try:
            # 加载文本文档
            self.text_documents = self._load_text_documents()
            
            # 加载图像文档
            self.image_documents = self._load_image_documents()
            
            # 加载映射关系
            self.mappings = self._load_mappings()
            
            logger.info(f"知识库加载完成: {len(self.text_documents)} 个文本文档, {len(self.image_documents)} 个图像文档")
        except Exception as e:
            logger.error(f"加载知识库失败: {e}")
            self.text_documents = []
            self.image_documents = []
            self.mappings = {}
    
    def _load_text_documents(self) -> List[Dict[str, Any]]:
        """加载文本文档"""
        documents = []
        try:
            if os.path.exists(self.text_path):
                for file_path in Path(self.text_path).rglob("*.txt"):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    documents.append({
                        'id': str(file_path.stem),
                        'title': file_path.stem,
                        'content': content,
                        'file_path': str(file_path),
                        'type': 'text',
                        'created_at': datetime.fromtimestamp(file_path.stat().st_ctime).isoformat()
                    })
        except Exception as e:
            logger.error(f"加载文本文档失败: {e}")
        
        return documents
    
    def _load_image_documents(self) -> List[Dict[str, Any]]:
        """加载图像文档"""
        documents = []
        try:
            if os.path.exists(self.images_path):
                for file_path in Path(self.images_path).rglob("*"):
                    if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                        documents.append({
                            'id': str(file_path.stem),
                            'title': file_path.stem,
                            'file_path': str(file_path),
                            'type': 'image',
                            'created_at': datetime.fromtimestamp(file_path.stat().st_ctime).isoformat()
                        })
        except Exception as e:
            logger.error(f"加载图像文档失败: {e}")
        
        return documents
    
    def _load_mappings(self) -> Dict[str, Any]:
        """加载映射关系"""
        mappings = {}
        try:
            mapping_file = os.path.join(self.mappings_path, "mappings.json")
            if os.path.exists(mapping_file):
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    mappings = json.load(f)
        except Exception as e:
            logger.error(f"加载映射关系失败: {e}")
        
        return mappings
    
    def add_text_document(self, title: str, content: str, metadata: Dict[str, Any] = None) -> str:
        """添加文本文档"""
        try:
            doc_id = f"text_{int(time.time())}"
            file_path = os.path.join(self.text_path, f"{doc_id}.txt")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            document = {
                'id': doc_id,
                'title': title,
                'content': content,
                'file_path': file_path,
                'type': 'text',
                'metadata': metadata or {},
                'created_at': datetime.now().isoformat()
            }
            
            self.text_documents.append(document)
            logger.info(f"添加文本文档成功: {title}")
            return doc_id
            
        except Exception as e:
            logger.error(f"添加文本文档失败: {e}")
            return ""
    
    def add_image_document(self, title: str, image_path: str, metadata: Dict[str, Any] = None) -> str:
        """添加图像文档"""
        try:
            doc_id = f"image_{int(time.time())}"
            
            # 复制图像到知识库目录
            import shutil
            file_extension = Path(image_path).suffix
            target_path = os.path.join(self.images_path, f"{doc_id}{file_extension}")
            shutil.copy2(image_path, target_path)
            
            document = {
                'id': doc_id,
                'title': title,
                'file_path': target_path,
                'type': 'image',
                'metadata': metadata or {},
                'created_at': datetime.now().isoformat()
            }
            
            self.image_documents.append(document)
            logger.info(f"添加图像文档成功: {title}")
            return doc_id
            
        except Exception as e:
            logger.error(f"添加图像文档失败: {e}")
            return ""
    
    def search_documents(self, query: str, doc_type: str = "all", limit: int = 10) -> List[Dict[str, Any]]:
        """搜索文档"""
        results = []
        
        try:
            if doc_type in ["all", "text"]:
                for doc in self.text_documents:
                    if query.lower() in doc['content'].lower() or query.lower() in doc['title'].lower():
                        results.append(doc)
            
            if doc_type in ["all", "image"]:
                for doc in self.image_documents:
                    if query.lower() in doc['title'].lower():
                        results.append(doc)
            
            # 按相关性排序（简单实现）
            results = sorted(results, key=lambda x: x['title'].lower().count(query.lower()), reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"搜索文档失败: {e}")
            return []
    
    def get_document_by_id(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取文档"""
        try:
            for doc in self.text_documents + self.image_documents:
                if doc['id'] == doc_id:
                    return doc
            return None
        except Exception as e:
            logger.error(f"获取文档失败: {e}")
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        return {
            'text_documents': len(self.text_documents),
            'image_documents': len(self.image_documents),
            'total_documents': len(self.text_documents) + len(self.image_documents),
            'last_updated': datetime.now().isoformat()
        }
