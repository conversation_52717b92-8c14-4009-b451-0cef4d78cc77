#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化检索引擎
集成多种检索策略，提供高质量的检索结果
"""

import os
import json
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from loguru import logger

try:
    from .text_retriever import TextRetriever
    from .multimodal_retriever import MultimodalRetriever
except ImportError:
    try:
        from text_retriever import TextRetriever
        from multimodal_retriever import MultimodalRetriever
    except ImportError:
        logger.error("无法导入检索器模块")
        TextRetriever = None
        MultimodalRetriever = None


class OptimizedRetrievalEngine:
    """优化检索引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # 初始化各种检索器
        self.text_retriever = None
        self.multimodal_retriever = None
        
        if TextRetriever:
            try:
                text_config = config.get("text_retrieval", {})
                self.text_retriever = TextRetriever(text_config)
                logger.info("文本检索器初始化成功")
            except Exception as e:
                logger.error(f"文本检索器初始化失败: {e}")
        
        if MultimodalRetriever:
            try:
                multimodal_config = config.get("multimodal_retrieval", {})
                self.multimodal_retriever = MultimodalRetriever(multimodal_config)
                logger.info("多模态检索器初始化成功")
            except Exception as e:
                logger.error(f"多模态检索器初始化失败: {e}")
        
        # 检索策略配置
        self.strategies = config.get("strategies", {
            "text_weight": 0.6,
            "image_weight": 0.4,
            "fusion_method": "weighted_sum",
            "rerank_enabled": True,
            "diversity_threshold": 0.8
        })
        
        # 缓存配置
        self.cache_enabled = config.get("cache_enabled", True)
        self.cache_size = config.get("cache_size", 1000)
        self.search_cache = {}
        
        logger.info("优化检索引擎初始化完成")
    
    def add_documents(self, documents: List[Dict[str, Any]], doc_type: str = "auto") -> bool:
        """添加文档到检索引擎"""
        try:
            success = True
            
            if doc_type in ["auto", "text"]:
                text_docs = [doc for doc in documents if doc.get('type') == 'text' or 'content' in doc]
                if text_docs and self.text_retriever:
                    success &= self.text_retriever.add_documents(text_docs)
            
            if doc_type in ["auto", "image"]:
                image_docs = [doc for doc in documents if doc.get('type') == 'image' or 'file_path' in doc]
                if image_docs and self.multimodal_retriever:
                    success &= self.multimodal_retriever.add_image_documents(image_docs)
            
            # 清空缓存
            if self.cache_enabled:
                self.search_cache.clear()
            
            return success
            
        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            return False
    
    def search(self, query: str, strategy: str = "hybrid", limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """执行搜索"""
        try:
            # 检查缓存
            cache_key = f"{query}_{strategy}_{limit}"
            if self.cache_enabled and cache_key in self.search_cache:
                logger.debug(f"从缓存返回搜索结果: {query[:50]}...")
                return self.search_cache[cache_key]
            
            # 执行搜索
            if strategy == "text_only":
                results = self._text_search(query, limit, **kwargs)
            elif strategy == "image_only":
                results = self._image_search(query, limit, **kwargs)
            elif strategy == "multimodal":
                results = self._multimodal_search(query, limit, **kwargs)
            elif strategy == "hybrid":
                results = self._hybrid_search(query, limit, **kwargs)
            else:
                logger.warning(f"未知搜索策略: {strategy}，使用混合搜索")
                results = self._hybrid_search(query, limit, **kwargs)
            
            # 后处理
            results = self._post_process_results(results, query)
            
            # 缓存结果
            if self.cache_enabled and len(self.search_cache) < self.cache_size:
                self.search_cache[cache_key] = results
            
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def _text_search(self, query: str, limit: int, **kwargs) -> List[Dict[str, Any]]:
        """纯文本搜索"""
        if not self.text_retriever:
            logger.warning("文本检索器不可用")
            return []
        
        return self.text_retriever.search(query, limit, **kwargs)
    
    def _image_search(self, query: str, limit: int, **kwargs) -> List[Dict[str, Any]]:
        """纯图像搜索"""
        if not self.multimodal_retriever:
            logger.warning("多模态检索器不可用")
            return []
        
        return self.multimodal_retriever.search_images(query, limit)
    
    def _multimodal_search(self, query: str, limit: int, **kwargs) -> List[Dict[str, Any]]:
        """多模态搜索"""
        if not self.multimodal_retriever:
            logger.warning("多模态检索器不可用")
            return []
        
        text_limit = limit // 2
        image_limit = limit - text_limit
        
        multimodal_results = self.multimodal_retriever.search_multimodal(query, text_limit, image_limit)
        
        # 合并结果
        all_results = multimodal_results['text'] + multimodal_results['images']
        
        # 重新排序
        return self._rerank_results(all_results, query)[:limit]
    
    def _hybrid_search(self, query: str, limit: int, **kwargs) -> List[Dict[str, Any]]:
        """混合搜索策略"""
        try:
            all_results = []
            
            # 文本搜索
            if self.text_retriever:
                text_results = self.text_retriever.search(query, limit * 2)
                for result in text_results:
                    result['source'] = 'text'
                    result['base_score'] = result.get('similarity', result.get('match_score', 0))
                all_results.extend(text_results)
            
            # 图像搜索
            if self.multimodal_retriever:
                image_results = self.multimodal_retriever.search_images(query, limit)
                for result in image_results:
                    result['source'] = 'image'
                    result['base_score'] = result.get('match_score', 0) / 10.0  # 归一化
                all_results.extend(image_results)
            
            # 融合评分
            all_results = self._fusion_scoring(all_results, query)
            
            # 多样性过滤
            if self.strategies.get("diversity_threshold"):
                all_results = self._diversity_filter(all_results)
            
            # 重排序
            if self.strategies.get("rerank_enabled"):
                all_results = self._rerank_results(all_results, query)
            
            return all_results[:limit]
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            return []
    
    def _fusion_scoring(self, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """融合评分"""
        try:
            text_weight = self.strategies.get("text_weight", 0.6)
            image_weight = self.strategies.get("image_weight", 0.4)
            
            for result in results:
                base_score = result.get('base_score', 0)
                source = result.get('source', 'text')
                
                if source == 'text':
                    result['fusion_score'] = base_score * text_weight
                elif source == 'image':
                    result['fusion_score'] = base_score * image_weight
                else:
                    result['fusion_score'] = base_score * 0.5
            
            # 按融合评分排序
            results.sort(key=lambda x: x.get('fusion_score', 0), reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"融合评分失败: {e}")
            return results
    
    def _diversity_filter(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """多样性过滤"""
        try:
            threshold = self.strategies.get("diversity_threshold", 0.8)
            filtered_results = []
            
            for result in results:
                is_diverse = True
                current_content = result.get('content', result.get('title', ''))
                
                for existing in filtered_results:
                    existing_content = existing.get('content', existing.get('title', ''))
                    
                    # 简单的相似度计算
                    similarity = self._calculate_text_similarity(current_content, existing_content)
                    
                    if similarity > threshold:
                        is_diverse = False
                        break
                
                if is_diverse:
                    filtered_results.append(result)
            
            return filtered_results
            
        except Exception as e:
            logger.error(f"多样性过滤失败: {e}")
            return results
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单实现）"""
        try:
            if not text1 or not text2:
                return 0.0
            
            words1 = set(text1.lower().split())
            words2 = set(text2.lower().split())
            
            if not words1 or not words2:
                return 0.0
            
            intersection = words1.intersection(words2)
            union = words1.union(words2)
            
            return len(intersection) / len(union)
            
        except Exception as e:
            logger.error(f"计算文本相似度失败: {e}")
            return 0.0
    
    def _rerank_results(self, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """重排序结果"""
        try:
            # 简单的重排序策略：结合多个因素
            for result in results:
                fusion_score = result.get('fusion_score', result.get('base_score', 0))
                
                # 考虑查询匹配度
                content = result.get('content', result.get('title', ''))
                query_match = content.lower().count(query.lower()) / max(len(content.split()), 1)
                
                # 考虑文档新鲜度
                created_at = result.get('created_at', result.get('indexed_at', ''))
                freshness_score = 0.1 if created_at else 0.0
                
                # 综合评分
                result['final_score'] = fusion_score + query_match * 0.2 + freshness_score
            
            # 按最终评分排序
            results.sort(key=lambda x: x.get('final_score', 0), reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"重排序失败: {e}")
            return results
    
    def _post_process_results(self, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """后处理结果"""
        try:
            # 添加搜索元数据
            for i, result in enumerate(results):
                result['rank'] = i + 1
                result['search_query'] = query
                result['search_timestamp'] = datetime.now().isoformat()
            
            return results
            
        except Exception as e:
            logger.error(f"后处理失败: {e}")
            return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'cache_size': len(self.search_cache) if self.cache_enabled else 0,
            'cache_enabled': self.cache_enabled,
            'strategies': self.strategies
        }
        
        if self.text_retriever:
            stats['text_retriever'] = self.text_retriever.get_statistics()
        
        if self.multimodal_retriever:
            stats['multimodal_retriever'] = self.multimodal_retriever.get_statistics()
        
        return stats
    
    def clear_cache(self):
        """清空缓存"""
        if self.cache_enabled:
            self.search_cache.clear()
            logger.info("搜索缓存已清空")
