#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统功能验证脚本
验证数据目录重构后系统的各项功能是否正常工作
"""

import os
import sys
import json
import requests
import time
from pathlib import Path
from typing import Dict, List, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))


class SystemFunctionalityTester:
    """系统功能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:5002"):
        self.base_url = base_url
        self.test_results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed_tests': 0,
                'failed_tests': 0,
                'success_rate': 0.0
            }
        }
    
    def test_server_availability(self) -> bool:
        """测试服务器可用性"""
        logger.info("测试服务器可用性...")
        try:
            response = requests.get(self.base_url, timeout=10)
            success = response.status_code == 200
            
            self.test_results['tests']['server_availability'] = {
                'status': 'PASS' if success else 'FAIL',
                'details': f"HTTP状态码: {response.status_code}",
                'response_time': response.elapsed.total_seconds()
            }
            
            if success:
                logger.info("✅ 服务器可用性测试通过")
            else:
                logger.error(f"❌ 服务器可用性测试失败: HTTP {response.status_code}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 服务器连接失败: {e}")
            self.test_results['tests']['server_availability'] = {
                'status': 'FAIL',
                'details': f"连接错误: {str(e)}",
                'response_time': None
            }
            return False
    
    def test_data_loading(self) -> bool:
        """测试数据加载功能"""
        logger.info("测试数据加载功能...")
        try:
            # 测试统一数据配置
            from core.unified_data_config import unified_data_config, DataLayer, DataType
            
            # 验证关键数据路径
            key_paths = [
                (DataLayer.ENHANCED, DataType.GENERATED),
                (DataLayer.PROCESSED, DataType.STRUCTURED),
                (DataLayer.PRODUCTION, DataType.ACTIVE),
                (DataLayer.OUTPUTS, DataType.REPORTS)
            ]
            
            path_results = {}
            for layer, data_type in key_paths:
                path = unified_data_config.get_path(layer, data_type)
                exists = path.exists()
                path_results[f"{layer.value}/{data_type.value}"] = {
                    'path': str(path),
                    'exists': exists
                }
            
            # 测试数据管理器
            from ui.app import RealDataManager
            data_manager = RealDataManager()
            
            # 测试数据加载
            case_studies = data_manager.get_case_studies()
            equipment_db = data_manager.get_equipment_database()
            fault_patterns = data_manager.get_fault_patterns()
            
            success = (
                len(case_studies) > 0 and
                len(equipment_db) > 0 and
                len(fault_patterns) > 0
            )
            
            self.test_results['tests']['data_loading'] = {
                'status': 'PASS' if success else 'FAIL',
                'details': {
                    'case_studies_count': len(case_studies),
                    'equipment_db_count': len(equipment_db),
                    'fault_patterns_count': len(fault_patterns),
                    'path_validation': path_results
                }
            }
            
            if success:
                logger.info(f"✅ 数据加载测试通过 - 案例:{len(case_studies)}, 设备:{len(equipment_db)}, 故障模式:{len(fault_patterns)}")
            else:
                logger.error("❌ 数据加载测试失败 - 数据为空")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 数据加载测试失败: {e}")
            self.test_results['tests']['data_loading'] = {
                'status': 'FAIL',
                'details': f"错误: {str(e)}"
            }
            return False
    
    def test_knowledge_base_search(self) -> bool:
        """测试知识库搜索功能"""
        logger.info("测试知识库搜索功能...")
        try:
            # 测试搜索API
            search_data = {
                'query': '变压器故障',
                'search_type': 'semantic',
                'top_k': 5
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/knowledge/search",
                json=search_data,
                timeout=30
            )
            
            if response.status_code == 200:
                results = response.json()
                success = (
                    'results' in results and
                    len(results['results']) > 0
                )
                
                self.test_results['tests']['knowledge_base_search'] = {
                    'status': 'PASS' if success else 'FAIL',
                    'details': {
                        'query': search_data['query'],
                        'results_count': len(results.get('results', [])),
                        'response_time': response.elapsed.total_seconds()
                    }
                }
                
                if success:
                    logger.info(f"✅ 知识库搜索测试通过 - 找到{len(results['results'])}个结果")
                else:
                    logger.error("❌ 知识库搜索测试失败 - 无搜索结果")
                
                return success
            else:
                logger.error(f"❌ 知识库搜索API失败: HTTP {response.status_code}")
                self.test_results['tests']['knowledge_base_search'] = {
                    'status': 'FAIL',
                    'details': f"HTTP错误: {response.status_code}"
                }
                return False
                
        except Exception as e:
            logger.error(f"❌ 知识库搜索测试失败: {e}")
            self.test_results['tests']['knowledge_base_search'] = {
                'status': 'FAIL',
                'details': f"错误: {str(e)}"
            }
            return False
    
    def test_deepseek_models(self) -> bool:
        """测试DeepSeek模型功能"""
        logger.info("测试DeepSeek模型功能...")
        try:
            # 测试DeepSeek-V3
            v3_data = {
                'query': '110kV变压器油温过高，请分析可能原因',
                'model': 'deepseek-v3',
                'use_rag': True
            }
            
            response_v3 = requests.post(
                f"{self.base_url}/api/v1/analyze",
                json=v3_data,
                timeout=60
            )
            
            v3_success = (
                response_v3.status_code == 200 and
                len(response_v3.text) > 100
            )
            
            # 测试DeepSeek-R1
            r1_data = {
                'query': '220kV断路器拒动，请进行故障分析',
                'model': 'deepseek-r1',
                'use_rag': True
            }
            
            response_r1 = requests.post(
                f"{self.base_url}/api/v1/analyze",
                json=r1_data,
                timeout=60
            )
            
            r1_success = (
                response_r1.status_code == 200 and
                len(response_r1.text) > 100
            )
            
            overall_success = v3_success and r1_success
            
            self.test_results['tests']['deepseek_models'] = {
                'status': 'PASS' if overall_success else 'FAIL',
                'details': {
                    'deepseek_v3': {
                        'status': 'PASS' if v3_success else 'FAIL',
                        'response_length': len(response_v3.text) if v3_success else 0,
                        'response_time': response_v3.elapsed.total_seconds() if v3_success else None
                    },
                    'deepseek_r1': {
                        'status': 'PASS' if r1_success else 'FAIL',
                        'response_length': len(response_r1.text) if r1_success else 0,
                        'response_time': response_r1.elapsed.total_seconds() if r1_success else None
                    }
                }
            }
            
            if overall_success:
                logger.info("✅ DeepSeek模型测试通过")
            else:
                logger.error("❌ DeepSeek模型测试失败")
            
            return overall_success
            
        except Exception as e:
            logger.error(f"❌ DeepSeek模型测试失败: {e}")
            self.test_results['tests']['deepseek_models'] = {
                'status': 'FAIL',
                'details': f"错误: {str(e)}"
            }
            return False
    
    def test_file_upload(self) -> bool:
        """测试文件上传功能"""
        logger.info("测试文件上传功能...")
        try:
            # 创建测试文件
            test_content = "测试文档内容：变压器运行参数监测报告"
            test_file_path = Path("test_upload.txt")
            
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            # 测试文档上传
            with open(test_file_path, 'rb') as f:
                files = {'file': ('test_upload.txt', f, 'text/plain')}
                response = requests.post(
                    f"{self.base_url}/api/v1/knowledge/documents/add",
                    files=files,
                    timeout=30
                )
            
            success = response.status_code == 200
            
            # 清理测试文件
            if test_file_path.exists():
                test_file_path.unlink()
            
            self.test_results['tests']['file_upload'] = {
                'status': 'PASS' if success else 'FAIL',
                'details': {
                    'http_status': response.status_code,
                    'response_time': response.elapsed.total_seconds()
                }
            }
            
            if success:
                logger.info("✅ 文件上传测试通过")
            else:
                logger.error(f"❌ 文件上传测试失败: HTTP {response.status_code}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 文件上传测试失败: {e}")
            self.test_results['tests']['file_upload'] = {
                'status': 'FAIL',
                'details': f"错误: {str(e)}"
            }
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("开始系统功能验证...")
        
        tests = [
            ('server_availability', self.test_server_availability),
            ('data_loading', self.test_data_loading),
            ('knowledge_base_search', self.test_knowledge_base_search),
            ('deepseek_models', self.test_deepseek_models),
            ('file_upload', self.test_file_upload)
        ]
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                if result:
                    self.test_results['summary']['passed_tests'] += 1
                else:
                    self.test_results['summary']['failed_tests'] += 1
                
                self.test_results['summary']['total_tests'] += 1
                
            except Exception as e:
                logger.error(f"测试 {test_name} 执行失败: {e}")
                self.test_results['summary']['failed_tests'] += 1
                self.test_results['summary']['total_tests'] += 1
        
        # 计算成功率
        if self.test_results['summary']['total_tests'] > 0:
            self.test_results['summary']['success_rate'] = (
                self.test_results['summary']['passed_tests'] / 
                self.test_results['summary']['total_tests'] * 100
            )
        
        # 保存测试报告
        self.save_test_report()
        
        return self.test_results
    
    def save_test_report(self):
        """保存测试报告"""
        try:
            report_path = Path("data/metadata/quality/system_functionality_test_report.json")
            report_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"测试报告保存到: {report_path}")
            
        except Exception as e:
            logger.error(f"保存测试报告失败: {e}")
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("系统功能验证报告")
        print("="*60)
        
        summary = self.test_results['summary']
        print(f"📊 测试统计:")
        print(f"   - 总测试数: {summary['total_tests']}")
        print(f"   - 通过测试: {summary['passed_tests']}")
        print(f"   - 失败测试: {summary['failed_tests']}")
        print(f"   - 成功率: {summary['success_rate']:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, test_result in self.test_results['tests'].items():
            status_icon = "✅" if test_result['status'] == 'PASS' else "❌"
            print(f"   {status_icon} {test_name}: {test_result['status']}")
        
        if summary['success_rate'] >= 80:
            print(f"\n🎉 系统功能验证通过！数据目录重构成功！")
        elif summary['success_rate'] >= 60:
            print(f"\n⚠️ 系统基本功能正常，但存在一些问题需要修复")
        else:
            print(f"\n❌ 系统存在严重问题，需要进一步调试")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="系统功能验证工具")
    parser.add_argument("--base-url", default="http://localhost:5002", help="服务器基础URL")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建测试器
    tester = SystemFunctionalityTester(args.base_url)
    
    # 运行所有测试
    results = tester.run_all_tests()
    
    # 打印摘要
    tester.print_summary()


if __name__ == "__main__":
    main()
