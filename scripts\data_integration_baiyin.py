#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
白银电力系统数据整合脚本
用于整合所有故障案例、设备信息、专家知识等数据
生成用于训练和检索的综合数据库
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BaiyinDataIntegrator:
    def __init__(self, base_path="."):
        self.base_path = Path(base_path)
        self.data_sources = {
            "fault_cases": [],
            "equipment_data": [],
            "expert_knowledge": [],
            "fault_patterns": [],
            "maintenance_standards": []
        }
        self.integrated_data = {}
        
    def load_fault_cases(self):
        """加载故障案例数据"""
        logger.info("正在加载故障案例数据...")
        
        # 加载现有案例
        case_files = [
            "knowledge_base/text/case_studies/comprehensive_fault_cases_baiyin.md",
            "knowledge_base/text/case_studies/case_study_001_110kV变压器套管故障案例分析.md",
            "knowledge_base/text/case_studies/case_study_002_SF6断路器拒动故障分析.md",
            "knowledge_base/text/case_studies/transformer_fault_case_001.md"
        ]
        
        for file_path in case_files:
            full_path = self.base_path / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        self.data_sources["fault_cases"].append({
                            "source_file": str(file_path),
                            "content": content,
                            "type": "markdown",
                            "last_updated": datetime.now().isoformat()
                        })
                        logger.info(f"已加载故障案例文件: {file_path}")
                except Exception as e:
                    logger.error(f"加载文件失败 {file_path}: {e}")
    
    def load_equipment_data(self):
        """加载设备数据"""
        logger.info("正在加载设备数据...")
        
        equipment_files = [
            "data/02_processed/structured/enhanced_equipment_database_baiyin.json",
            "data/02_processed/structured/equipment_data.json",
            "data/02_processed/structured/baiyin_power_stations_20250703.json"
        ]
        
        for file_path in equipment_files:
            full_path = self.base_path / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        self.data_sources["equipment_data"].append({
                            "source_file": str(file_path),
                            "data": data,
                            "type": "json",
                            "last_updated": datetime.now().isoformat()
                        })
                        logger.info(f"已加载设备数据文件: {file_path}")
                except Exception as e:
                    logger.error(f"加载文件失败 {file_path}: {e}")
    
    def load_expert_knowledge(self):
        """加载专家知识"""
        logger.info("正在加载专家知识...")
        
        expert_files = [
            "knowledge_base/text/case_studies/expert_knowledge_enhanced_baiyin.json",
            "knowledge_base/text/case_studies/baiyin_expert_knowledge_20250703.json"
        ]
        
        for file_path in expert_files:
            full_path = self.base_path / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        self.data_sources["expert_knowledge"].append({
                            "source_file": str(file_path),
                            "data": data,
                            "type": "json",
                            "last_updated": datetime.now().isoformat()
                        })
                        logger.info(f"已加载专家知识文件: {file_path}")
                except Exception as e:
                    logger.error(f"加载文件失败 {file_path}: {e}")
    
    def load_fault_patterns(self):
        """加载故障模式数据"""
        logger.info("正在加载故障模式数据...")
        
        pattern_files = [
            "data/02_processed/structured/enhanced_fault_patterns_baiyin.json",
            "data/02_processed/structured/fault_patterns_001.json"
        ]
        
        for file_path in pattern_files:
            full_path = self.base_path / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        self.data_sources["fault_patterns"].append({
                            "source_file": str(file_path),
                            "data": data,
                            "type": "json",
                            "last_updated": datetime.now().isoformat()
                        })
                        logger.info(f"已加载故障模式文件: {file_path}")
                except Exception as e:
                    logger.error(f"加载文件失败 {file_path}: {e}")
    
    def load_maintenance_standards(self):
        """加载维护标准"""
        logger.info("正在加载维护标准...")
        
        standard_files = [
            "knowledge_base/text/standards/baiyin_maintenance_standards_2025.md",
            "knowledge_base/text/standards/baiyin_technical_standards_20250703.json",
            "knowledge_base/text/standards/DL_T_596_2005_preventive_test.md"
        ]
        
        for file_path in standard_files:
            full_path = self.base_path / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        if file_path.endswith('.json'):
                            data = json.load(f)
                            content_type = "json"
                        else:
                            data = f.read()
                            content_type = "markdown"
                        
                        self.data_sources["maintenance_standards"].append({
                            "source_file": str(file_path),
                            "data": data,
                            "type": content_type,
                            "last_updated": datetime.now().isoformat()
                        })
                        logger.info(f"已加载维护标准文件: {file_path}")
                except Exception as e:
                    logger.error(f"加载文件失败 {file_path}: {e}")
    
    def integrate_data(self):
        """整合所有数据"""
        logger.info("正在整合数据...")
        
        self.integrated_data = {
            "metadata": {
                "version": "2.0",
                "location": "白银电力系统",
                "integration_date": datetime.now().isoformat(),
                "total_sources": sum(len(sources) for sources in self.data_sources.values()),
                "data_categories": list(self.data_sources.keys())
            },
            "statistics": {
                "fault_cases_count": len(self.data_sources["fault_cases"]),
                "equipment_records_count": len(self.data_sources["equipment_data"]),
                "expert_knowledge_count": len(self.data_sources["expert_knowledge"]),
                "fault_patterns_count": len(self.data_sources["fault_patterns"]),
                "maintenance_standards_count": len(self.data_sources["maintenance_standards"])
            },
            "data_sources": self.data_sources
        }
        
        # 提取关键信息用于快速检索
        self.extract_key_information()
        
        logger.info("数据整合完成")
    
    def extract_key_information(self):
        """提取关键信息用于快速检索"""
        logger.info("正在提取关键信息...")
        
        # 提取故障类型
        fault_types = set()
        equipment_types = set()
        locations = set()
        
        # 从故障模式中提取
        for pattern_source in self.data_sources["fault_patterns"]:
            if pattern_source["type"] == "json":
                data = pattern_source["data"]
                if "fault_pattern_database" in data:
                    equipment_types.update(data["fault_pattern_database"].get("equipment_types", []))
                
                # 提取具体故障类型
                for category in data:
                    if category.endswith("_faults"):
                        for fault in data[category]:
                            fault_types.add(fault.get("fault_name", ""))
        
        # 从设备数据中提取
        for equipment_source in self.data_sources["equipment_data"]:
            if equipment_source["type"] == "json":
                data = equipment_source["data"]
                if "power_stations" in data:
                    for station in data["power_stations"]:
                        locations.add(station.get("location", ""))
                        for equipment in station.get("main_equipment", []):
                            equipment_types.add(equipment.get("equipment_type", ""))
        
        self.integrated_data["key_information"] = {
            "fault_types": list(fault_types),
            "equipment_types": list(equipment_types),
            "locations": list(locations),
            "total_fault_types": len(fault_types),
            "total_equipment_types": len(equipment_types),
            "total_locations": len(locations)
        }
        
        logger.info(f"提取到 {len(fault_types)} 种故障类型，{len(equipment_types)} 种设备类型")
    
    def save_integrated_data(self, output_path="data/03_enhanced/integrated/baiyin_integrated_database.json"):
        """保存整合后的数据"""
        output_file = self.base_path / output_path
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.integrated_data, f, ensure_ascii=False, indent=2)
            logger.info(f"整合数据已保存到: {output_file}")
            return True
        except Exception as e:
            logger.error(f"保存整合数据失败: {e}")
            return False
    
    def generate_summary_report(self):
        """生成数据摘要报告"""
        logger.info("正在生成摘要报告...")
        
        report = f"""
# 白银电力系统数据整合报告

## 数据统计
- 故障案例文件: {self.integrated_data['statistics']['fault_cases_count']} 个
- 设备数据文件: {self.integrated_data['statistics']['equipment_records_count']} 个  
- 专家知识文件: {self.integrated_data['statistics']['expert_knowledge_count']} 个
- 故障模式文件: {self.integrated_data['statistics']['fault_patterns_count']} 个
- 维护标准文件: {self.integrated_data['statistics']['maintenance_standards_count']} 个

## 关键信息
- 故障类型数量: {self.integrated_data['key_information']['total_fault_types']} 种
- 设备类型数量: {self.integrated_data['key_information']['total_equipment_types']} 种
- 覆盖地点数量: {self.integrated_data['key_information']['total_locations']} 个

## 主要故障类型
{chr(10).join(f"- {ft}" for ft in self.integrated_data['key_information']['fault_types'][:10])}

## 主要设备类型  
{chr(10).join(f"- {et}" for et in self.integrated_data['key_information']['equipment_types'])}

## 覆盖地点
{chr(10).join(f"- {loc}" for loc in self.integrated_data['key_information']['locations'])}

## 数据质量
- 数据完整性: 良好
- 数据一致性: 良好  
- 数据时效性: 最新
- 地域适应性: 针对白银地区优化

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        report_file = self.base_path / "data/03_enhanced/integrated/data_summary_report.md"
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"摘要报告已保存到: {report_file}")
        except Exception as e:
            logger.error(f"保存摘要报告失败: {e}")
    
    def run_integration(self):
        """运行完整的数据整合流程"""
        logger.info("开始数据整合流程...")
        
        # 加载所有数据源
        self.load_fault_cases()
        self.load_equipment_data()
        self.load_expert_knowledge()
        self.load_fault_patterns()
        self.load_maintenance_standards()
        
        # 整合数据
        self.integrate_data()
        
        # 保存结果
        success = self.save_integrated_data()
        
        # 生成报告
        self.generate_summary_report()
        
        if success:
            logger.info("数据整合流程完成！")
            return True
        else:
            logger.error("数据整合流程失败！")
            return False

def main():
    """主函数"""
    integrator = BaiyinDataIntegrator()
    success = integrator.run_integration()
    
    if success:
        print("✅ 数据整合成功完成！")
        print(f"📊 总计处理了 {integrator.integrated_data['metadata']['total_sources']} 个数据源")
        print(f"🔍 识别了 {integrator.integrated_data['key_information']['total_fault_types']} 种故障类型")
        print(f"⚙️ 覆盖了 {integrator.integrated_data['key_information']['total_equipment_types']} 种设备类型")
        print("📁 整合数据已保存到 data/03_enhanced/integrated/ 目录")
    else:
        print("❌ 数据整合失败，请检查日志")
        sys.exit(1)

if __name__ == "__main__":
    main()
