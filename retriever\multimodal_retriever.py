"""
多模态检索模块

支持文本、图像等多种模态的检索功能
"""

import os
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

try:
    from .text_retriever import TextRetriever
except ImportError:
    from text_retriever import TextRetriever


class MultimodalRetriever:
    """多模态检索器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # 初始化文本检索器
        text_config = config.get("text_retrieval", {})
        self.text_retriever = TextRetriever(text_config)
        
        # 图像检索配置
        self.image_index_path = config.get("image_index_path", "./embeddings/image_index")
        self.image_metadata_path = config.get("image_metadata_path", "./embeddings/image_metadata.json")
        
        # 图像文档存储
        self.image_documents = []
        self.image_metadata = {}
        
        # 加载图像索引
        self._load_image_index()
    
    def _load_image_index(self):
        """加载图像索引"""
        try:
            if os.path.exists(self.image_metadata_path):
                with open(self.image_metadata_path, 'r', encoding='utf-8') as f:
                    self.image_metadata = json.load(f)
                
                self.image_documents = self.image_metadata.get('documents', [])
                logger.info(f"加载图像索引: {len(self.image_documents)} 个图像")
            else:
                logger.info("未找到现有图像索引，将创建新索引")
        except Exception as e:
            logger.error(f"加载图像索引失败: {e}")
            self.image_documents = []
            self.image_metadata = {}
    
    def add_text_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文本文档"""
        return self.text_retriever.add_documents(documents)
    
    def add_image_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加图像文档"""
        try:
            for doc in documents:
                if 'id' not in doc or 'file_path' not in doc:
                    logger.warning("图像文档缺少必要字段，跳过")
                    continue
                
                # 检查是否已存在
                existing_ids = [d['id'] for d in self.image_documents]
                if doc['id'] in existing_ids:
                    logger.warning(f"图像文档 {doc['id']} 已存在，跳过")
                    continue
                
                # 添加时间戳
                doc['indexed_at'] = datetime.now().isoformat()
                self.image_documents.append(doc)
            
            # 保存索引
            self._save_image_index()
            
            logger.info(f"成功添加 {len(documents)} 个图像文档到索引")
            return True
            
        except Exception as e:
            logger.error(f"添加图像文档到索引失败: {e}")
            return False
    
    def _save_image_index(self):
        """保存图像索引"""
        try:
            os.makedirs(os.path.dirname(self.image_metadata_path), exist_ok=True)
            
            self.image_metadata = {
                'documents': self.image_documents,
                'total_documents': len(self.image_documents),
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.image_metadata_path, 'w', encoding='utf-8') as f:
                json.dump(self.image_metadata, f, ensure_ascii=False, indent=2)
            
            logger.info("图像索引保存成功")
        except Exception as e:
            logger.error(f"保存图像索引失败: {e}")
    
    def search_text(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索文本文档"""
        try:
            results = self.text_retriever.search(query, limit)
            
            # 添加模态标识
            for result in results:
                result['modality'] = 'text'
            
            return results
        except Exception as e:
            logger.error(f"文本搜索失败: {e}")
            return []
    
    def search_images(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索图像文档"""
        try:
            query_lower = query.lower()
            results = []
            
            for doc in self.image_documents:
                title = doc.get('title', '').lower()
                description = doc.get('description', '').lower()
                tags = doc.get('tags', [])
                
                # 计算匹配度
                title_matches = title.count(query_lower) * 3  # 标题权重最高
                desc_matches = description.count(query_lower) * 2
                tag_matches = sum(1 for tag in tags if query_lower in tag.lower())
                
                total_matches = title_matches + desc_matches + tag_matches
                
                if total_matches > 0:
                    doc_copy = doc.copy()
                    doc_copy['match_score'] = total_matches
                    doc_copy['modality'] = 'image'
                    results.append(doc_copy)
            
            # 按匹配度排序
            results.sort(key=lambda x: x['match_score'], reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"图像搜索失败: {e}")
            return []
    
    def search_multimodal(self, query: str, text_limit: int = 5, image_limit: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """多模态搜索"""
        try:
            # 并行搜索文本和图像
            text_results = self.search_text(query, text_limit)
            image_results = self.search_images(query, image_limit)
            
            return {
                'text': text_results,
                'images': image_results,
                'total_results': len(text_results) + len(image_results)
            }
            
        except Exception as e:
            logger.error(f"多模态搜索失败: {e}")
            return {'text': [], 'images': [], 'total_results': 0}
    
    def search_unified(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """统一搜索（合并所有模态结果）"""
        try:
            # 获取各模态结果
            text_results = self.search_text(query, limit)
            image_results = self.search_images(query, limit)
            
            # 合并结果
            all_results = text_results + image_results
            
            # 统一评分（简单实现）
            for result in all_results:
                if 'similarity' in result:
                    result['unified_score'] = result['similarity']
                elif 'match_score' in result:
                    result['unified_score'] = result['match_score'] / 10.0  # 归一化
                else:
                    result['unified_score'] = 0.5
            
            # 按统一评分排序
            all_results.sort(key=lambda x: x['unified_score'], reverse=True)
            
            return all_results[:limit]
            
        except Exception as e:
            logger.error(f"统一搜索失败: {e}")
            return []
    
    def get_document_by_id(self, doc_id: str, modality: str = None) -> Optional[Dict[str, Any]]:
        """根据ID获取文档"""
        try:
            if modality == 'text' or modality is None:
                text_doc = self.text_retriever.get_document_by_id(doc_id)
                if text_doc:
                    return text_doc
            
            if modality == 'image' or modality is None:
                for doc in self.image_documents:
                    if doc['id'] == doc_id:
                        return doc
            
            return None
        except Exception as e:
            logger.error(f"获取文档失败: {e}")
            return None
    
    def delete_document(self, doc_id: str, modality: str) -> bool:
        """删除文档"""
        try:
            if modality == 'text':
                return self.text_retriever.delete_document(doc_id)
            elif modality == 'image':
                original_count = len(self.image_documents)
                self.image_documents = [doc for doc in self.image_documents if doc['id'] != doc_id]
                
                if len(self.image_documents) < original_count:
                    self._save_image_index()
                    logger.info(f"成功删除图像文档: {doc_id}")
                    return True
                else:
                    logger.warning(f"未找到要删除的图像文档: {doc_id}")
                    return False
            else:
                logger.error(f"不支持的模态类型: {modality}")
                return False
                
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        text_stats = self.text_retriever.get_statistics()
        
        return {
            'text_documents': text_stats['total_documents'],
            'image_documents': len(self.image_documents),
            'total_documents': text_stats['total_documents'] + len(self.image_documents),
            'text_retriever_available': True,
            'image_index_path': self.image_index_path,
            'last_updated': max(
                text_stats.get('last_updated', ''),
                self.image_metadata.get('last_updated', '')
            )
        }
