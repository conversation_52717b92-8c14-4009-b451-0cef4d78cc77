#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强数据处理脚本
实现数据准备、清洗、脱敏、向量化和入库的完整流程
符合国网数据安全标准和白银电力系统要求
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from data_processing.data_standardizer import DataStandardizer
from data_processing.text_processor import TextProcessor
from data_processing.vector_processor import VectorProcessor
from data_processing.data_privacy import DataPrivacyProcessor


class EnhancedDataProcessor:
    """增强数据处理器 - 完整的数据准备和向量化流程"""
    
    def __init__(self, config_path: str = "configs/config.yaml"):
        self.config = self._load_config(config_path)
        
        # 初始化处理器
        self.standardizer = DataStandardizer()
        self.text_processor = TextProcessor(self.config.get("data_processing", {}))
        self.vector_processor = VectorProcessor(self.config.get("vector_processing", {}))
        self.privacy_processor = DataPrivacyProcessor(self.config.get("data_privacy", {}))
        
        # 处理统计
        self.processing_stats = {
            "start_time": None,
            "end_time": None,
            "total_files": 0,
            "processed_files": 0,
            "failed_files": 0,
            "total_documents": 0,
            "total_vectors": 0,
            "errors": []
        }

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            logger.warning(f"配置文件加载失败，使用默认配置: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "data_processing": {
                "chunk_size": 500,
                "chunk_overlap": 50,
                "max_chunk_size": 1000
            },
            "vector_processing": {
                "model_name": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
                "model_path": "./models/embedding",
                "dimension": 384,
                "batch_size": 32,
                "vector_db_type": "chroma",  # 使用Chroma作为默认向量数据库
                "chroma_path": "./embeddings/chroma_store",
                "chroma_collection_name": "baiyin_power_fault_collection"
            },
            "data_privacy": {
                "enable_anonymization": True,
                "preserve_technical_terms": True
            }
        }

    def process_baiyin_data(self, data_dir: str = "data", output_dir: str = "data/04_production/cached") -> bool:
        """
        处理白银电力系统数据的完整流程
        
        Args:
            data_dir: 数据目录
            output_dir: 输出目录
            
        Returns:
            是否成功
        """
        try:
            self.processing_stats["start_time"] = datetime.now()
            logger.info("开始白银电力系统数据处理流程...")
            
            # 1. 数据收集和预处理
            logger.info("步骤1: 数据收集和预处理")
            raw_documents = self._collect_raw_data(data_dir)
            if not raw_documents:
                logger.error("未找到可处理的数据文件")
                return False
            
            # 2. 数据标准化
            logger.info("步骤2: 数据标准化处理")
            standardized_documents = self._standardize_documents(raw_documents)
            
            # 3. 数据脱敏（如果启用）
            if self.config.get("data_privacy", {}).get("enable_anonymization", True):
                logger.info("步骤3: 数据脱敏处理")
                anonymized_documents = self._anonymize_documents(standardized_documents)
            else:
                anonymized_documents = standardized_documents
            
            # 4. 文本分块和结构化
            logger.info("步骤4: 文本分块和结构化")
            chunked_documents = self._chunk_documents(anonymized_documents)
            
            # 5. 向量化处理
            logger.info("步骤5: 向量化处理")
            success = self._vectorize_and_store(chunked_documents)
            
            # 6. 保存处理结果
            logger.info("步骤6: 保存处理结果")
            self._save_processed_data(chunked_documents, output_dir)
            
            # 7. 生成处理报告
            self._generate_processing_report(output_dir)
            
            self.processing_stats["end_time"] = datetime.now()
            
            if success:
                logger.info("白银电力系统数据处理流程完成！")
                return True
            else:
                logger.error("数据处理流程中出现错误")
                return False
                
        except Exception as e:
            logger.error(f"数据处理流程失败: {str(e)}")
            self.processing_stats["errors"].append(str(e))
            return False

    def _collect_raw_data(self, data_dir: str) -> List[Dict[str, Any]]:
        """收集原始数据"""
        try:
            documents = []
            data_path = Path(data_dir)
            
            # 支持的文件类型
            supported_extensions = ['.json', '.md', '.txt']
            
            # 遍历数据目录
            for subdir in ['structured', 'integrated', 'raw']:
                subdir_path = data_path / subdir
                if subdir_path.exists():
                    for file_path in subdir_path.rglob("*"):
                        if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                            try:
                                doc = self._load_document(file_path)
                                if doc:
                                    documents.append(doc)
                                    self.processing_stats["total_files"] += 1
                            except Exception as e:
                                logger.error(f"加载文件失败 {file_path}: {e}")
                                self.processing_stats["failed_files"] += 1
            
            logger.info(f"收集到 {len(documents)} 个文档")
            return documents
            
        except Exception as e:
            logger.error(f"数据收集失败: {str(e)}")
            return []

    def _load_document(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载单个文档"""
        try:
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 处理JSON数据结构
                if isinstance(data, dict):
                    # 提取文本内容
                    content = self._extract_text_from_json(data)
                    return {
                        "source": str(file_path),
                        "content": content,
                        "type": "json",
                        "metadata": {
                            "file_type": "json",
                            "location": "白银",
                            "data_type": self._infer_data_type(file_path.name)
                        }
                    }
                    
            elif file_path.suffix.lower() in ['.md', '.txt']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                return {
                    "source": str(file_path),
                    "content": content,
                    "type": "text",
                    "metadata": {
                        "file_type": file_path.suffix.lower(),
                        "location": "白银",
                        "data_type": self._infer_data_type(file_path.name)
                    }
                }
            
            return None
            
        except Exception as e:
            logger.error(f"文档加载失败 {file_path}: {e}")
            return None

    def _extract_text_from_json(self, data: Any) -> str:
        """从JSON数据中提取文本内容"""
        try:
            if isinstance(data, str):
                return data
            elif isinstance(data, dict):
                text_parts = []
                for key, value in data.items():
                    if isinstance(value, str) and len(value) > 10:
                        text_parts.append(f"{key}: {value}")
                    elif isinstance(value, (dict, list)):
                        sub_text = self._extract_text_from_json(value)
                        if sub_text:
                            text_parts.append(sub_text)
                return "\n".join(text_parts)
            elif isinstance(data, list):
                text_parts = []
                for item in data:
                    sub_text = self._extract_text_from_json(item)
                    if sub_text:
                        text_parts.append(sub_text)
                return "\n".join(text_parts)
            else:
                return str(data)
                
        except Exception as e:
            logger.error(f"JSON文本提取失败: {e}")
            return ""

    def _infer_data_type(self, filename: str) -> str:
        """推断数据类型"""
        filename_lower = filename.lower()
        
        if any(keyword in filename_lower for keyword in ['fault', '故障', 'case']):
            return "fault_case"
        elif any(keyword in filename_lower for keyword in ['equipment', '设备']):
            return "equipment_data"
        elif any(keyword in filename_lower for keyword in ['expert', '专家', 'knowledge']):
            return "expert_knowledge"
        elif any(keyword in filename_lower for keyword in ['standard', '标准']):
            return "technical_standard"
        else:
            return "general"

    def _standardize_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """标准化文档"""
        try:
            standardized_docs = []
            
            for doc in documents:
                content = doc.get("content", "")
                if content:
                    standardized_content = self.standardizer.standardize_text(content)
                    
                    standardized_doc = doc.copy()
                    standardized_doc["content"] = standardized_content
                    standardized_docs.append(standardized_doc)
                    
                    self.processing_stats["processed_files"] += 1
            
            logger.info(f"标准化处理完成，处理 {len(standardized_docs)} 个文档")
            return standardized_docs
            
        except Exception as e:
            logger.error(f"文档标准化失败: {e}")
            return documents

    def _anonymize_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """脱敏处理文档"""
        try:
            anonymized_docs = []
            
            for doc in documents:
                content = doc.get("content", "")
                if content:
                    anonymized_content = self.privacy_processor.anonymize_text(content)
                    
                    anonymized_doc = doc.copy()
                    anonymized_doc["content"] = anonymized_content
                    anonymized_docs.append(anonymized_doc)
            
            logger.info(f"脱敏处理完成，处理 {len(anonymized_docs)} 个文档")
            return anonymized_docs
            
        except Exception as e:
            logger.error(f"文档脱敏失败: {e}")
            return documents

    def _chunk_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分块处理文档"""
        try:
            chunked_docs = []
            
            for doc in documents:
                chunks = self.text_processor.split_text(
                    doc.get("content", ""),
                    doc.get("metadata", {})
                )
                
                for i, chunk in enumerate(chunks):
                    chunked_doc = {
                        "source": doc.get("source", ""),
                        "content": chunk,
                        "chunk_id": i,
                        "metadata": {
                            **doc.get("metadata", {}),
                            "chunk_index": i,
                            "total_chunks": len(chunks)
                        }
                    }
                    chunked_docs.append(chunked_doc)
            
            self.processing_stats["total_documents"] = len(chunked_docs)
            logger.info(f"文档分块完成，生成 {len(chunked_docs)} 个文档块")
            return chunked_docs
            
        except Exception as e:
            logger.error(f"文档分块失败: {e}")
            return documents

    def _vectorize_and_store(self, documents: List[Dict[str, Any]]) -> bool:
        """向量化并存储到数据库"""
        try:
            # 使用Chroma数据库
            if self.vector_processor.vector_db_type == "chroma":
                success = self.vector_processor.add_documents_to_chroma(documents)
                if success:
                    self.processing_stats["total_vectors"] = len(documents)
                    logger.info(f"成功将 {len(documents)} 个文档向量化并存储到Chroma数据库")
                return success
            
            # 使用FAISS（备选方案）
            else:
                vectors, metadata = self.vector_processor.process_documents(documents)
                if vectors.size > 0:
                    # 创建FAISS索引
                    index = self.vector_processor.create_faiss_index(vectors)
                    
                    # 保存索引和元数据
                    index_path = "embeddings/faiss_store/baiyin_power_index.faiss"
                    metadata_path = "embeddings/faiss_store/baiyin_power_metadata.pkl"
                    
                    self.vector_processor.save_index(index, index_path)
                    self.vector_processor.save_vectors_metadata(vectors, metadata, metadata_path)
                    
                    self.processing_stats["total_vectors"] = len(vectors)
                    logger.info(f"成功将 {len(vectors)} 个向量存储到FAISS索引")
                    return True
                
            return False
            
        except Exception as e:
            logger.error(f"向量化存储失败: {e}")
            return False

    def _save_processed_data(self, documents: List[Dict[str, Any]], output_dir: str):
        """保存处理后的数据"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 保存处理后的文档
            processed_file = output_path / "baiyin_processed_documents.json"
            with open(processed_file, 'w', encoding='utf-8') as f:
                json.dump(documents, f, ensure_ascii=False, indent=2)
            
            logger.info(f"处理后的数据已保存到: {processed_file}")
            
        except Exception as e:
            logger.error(f"保存处理数据失败: {e}")

    def _generate_processing_report(self, output_dir: str):
        """生成处理报告"""
        try:
            duration = (self.processing_stats["end_time"] - self.processing_stats["start_time"]).total_seconds()
            
            report = f"""
# 白银电力系统数据处理报告

## 处理统计
- 开始时间: {self.processing_stats["start_time"].strftime('%Y-%m-%d %H:%M:%S')}
- 结束时间: {self.processing_stats["end_time"].strftime('%Y-%m-%d %H:%M:%S')}
- 处理耗时: {duration:.2f} 秒
- 总文件数: {self.processing_stats["total_files"]}
- 成功处理: {self.processing_stats["processed_files"]}
- 失败文件: {self.processing_stats["failed_files"]}
- 生成文档块: {self.processing_stats["total_documents"]}
- 生成向量: {self.processing_stats["total_vectors"]}

## 处理流程
1. ✅ 数据收集和预处理
2. ✅ 数据标准化处理
3. ✅ 数据脱敏处理
4. ✅ 文本分块和结构化
5. ✅ 向量化处理
6. ✅ 数据库存储

## 数据质量
- 数据完整性: {'良好' if self.processing_stats['failed_files'] == 0 else '一般'}
- 处理成功率: {(self.processing_stats['processed_files'] / max(self.processing_stats['total_files'], 1) * 100):.1f}%
- 向量化成功率: {(self.processing_stats['total_vectors'] / max(self.processing_stats['total_documents'], 1) * 100):.1f}%

## 配置信息
- 向量数据库: {self.vector_processor.vector_db_type.upper()}
- 嵌入模型: {self.vector_processor.model_name}
- 数据脱敏: {'启用' if self.config.get('data_privacy', {}).get('enable_anonymization', True) else '禁用'}

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            report_file = Path(output_dir) / "processing_report.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"处理报告已保存到: {report_file}")
            
        except Exception as e:
            logger.error(f"生成处理报告失败: {e}")


def main():
    """主函数"""
    processor = EnhancedDataProcessor()
    
    logger.info("开始白银电力系统增强数据处理...")
    success = processor.process_baiyin_data()
    
    if success:
        print("✅ 数据处理成功完成！")
        print(f"📊 处理了 {processor.processing_stats['total_files']} 个文件")
        print(f"📝 生成了 {processor.processing_stats['total_documents']} 个文档块")
        print(f"🔢 创建了 {processor.processing_stats['total_vectors']} 个向量")
        print("📁 处理结果已保存到 data/04_production/cached/ 目录")
    else:
        print("❌ 数据处理失败，请检查日志")
        sys.exit(1)


if __name__ == "__main__":
    main()
