#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据配置管理
基于新的数据目录结构提供统一的数据路径管理
"""

import os
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum


class DataLayer(Enum):
    """数据层级枚举"""
    RAW = "01_raw"
    PROCESSED = "02_processed"
    ENHANCED = "03_enhanced"
    PRODUCTION = "04_production"
    OUTPUTS = "05_outputs"
    METADATA = "metadata"


class DataType(Enum):
    """数据类型枚举"""
    # 原始数据类型
    EQUIPMENT = "equipment"
    FAULT_CASES = "fault_cases"
    SENSOR_DATA = "sensor_data"
    MAINTENANCE_LOGS = "maintenance_logs"
    INSPECTION_REPORTS = "inspection_reports"
    UPLOADS = "uploads"
    
    # 处理数据类型
    CLEANED = "cleaned"
    ANNOTATED = "annotated"
    STRUCTURED = "structured"
    VALIDATED = "validated"
    
    # 增强数据类型
    GENERATED = "generated"
    INTEGRATED = "integrated"
    KNOWLEDGE_BASE = "knowledge_base"
    TRAINING_SETS = "training_sets"
    
    # 生产数据类型
    ACTIVE = "active"
    CACHED = "cached"
    INDEXED = "indexed"
    BACKUPS = "backups"
    
    # 输出数据类型
    REPORTS = "reports"
    EXPORTS = "exports"
    VISUALIZATIONS = "visualizations"
    LOGS = "logs"


@dataclass
class DataPath:
    """数据路径配置"""
    layer: DataLayer
    data_type: DataType
    description: str
    read_only: bool = False
    auto_create: bool = True


class UnifiedDataConfig:
    """统一数据配置管理器"""
    
    def __init__(self, base_path: str = "data"):
        self.base_path = Path(base_path)
        self._initialize_path_config()
    
    def _initialize_path_config(self):
        """初始化路径配置"""
        self.path_config = {
            # 原始数据层配置
            (DataLayer.RAW, DataType.EQUIPMENT): DataPath(
                DataLayer.RAW, DataType.EQUIPMENT, "设备原始数据", read_only=True
            ),
            (DataLayer.RAW, DataType.FAULT_CASES): DataPath(
                DataLayer.RAW, DataType.FAULT_CASES, "故障案例原始数据", read_only=True
            ),
            (DataLayer.RAW, DataType.SENSOR_DATA): DataPath(
                DataLayer.RAW, DataType.SENSOR_DATA, "传感器数据", read_only=True
            ),
            (DataLayer.RAW, DataType.MAINTENANCE_LOGS): DataPath(
                DataLayer.RAW, DataType.MAINTENANCE_LOGS, "维护日志", read_only=True
            ),
            (DataLayer.RAW, DataType.INSPECTION_REPORTS): DataPath(
                DataLayer.RAW, DataType.INSPECTION_REPORTS, "巡检报告", read_only=True
            ),
            (DataLayer.RAW, DataType.UPLOADS): DataPath(
                DataLayer.RAW, DataType.UPLOADS, "用户上传文件"
            ),
            
            # 处理数据层配置
            (DataLayer.PROCESSED, DataType.CLEANED): DataPath(
                DataLayer.PROCESSED, DataType.CLEANED, "清洗后数据"
            ),
            (DataLayer.PROCESSED, DataType.ANNOTATED): DataPath(
                DataLayer.PROCESSED, DataType.ANNOTATED, "标注数据"
            ),
            (DataLayer.PROCESSED, DataType.STRUCTURED): DataPath(
                DataLayer.PROCESSED, DataType.STRUCTURED, "结构化数据"
            ),
            (DataLayer.PROCESSED, DataType.VALIDATED): DataPath(
                DataLayer.PROCESSED, DataType.VALIDATED, "验证后数据"
            ),
            
            # 增强数据层配置
            (DataLayer.ENHANCED, DataType.GENERATED): DataPath(
                DataLayer.ENHANCED, DataType.GENERATED, "AI生成数据"
            ),
            (DataLayer.ENHANCED, DataType.INTEGRATED): DataPath(
                DataLayer.ENHANCED, DataType.INTEGRATED, "集成数据"
            ),
            (DataLayer.ENHANCED, DataType.KNOWLEDGE_BASE): DataPath(
                DataLayer.ENHANCED, DataType.KNOWLEDGE_BASE, "知识库数据"
            ),
            (DataLayer.ENHANCED, DataType.TRAINING_SETS): DataPath(
                DataLayer.ENHANCED, DataType.TRAINING_SETS, "训练数据集"
            ),
            
            # 生产数据层配置
            (DataLayer.PRODUCTION, DataType.ACTIVE): DataPath(
                DataLayer.PRODUCTION, DataType.ACTIVE, "当前使用数据"
            ),
            (DataLayer.PRODUCTION, DataType.CACHED): DataPath(
                DataLayer.PRODUCTION, DataType.CACHED, "缓存数据"
            ),
            (DataLayer.PRODUCTION, DataType.INDEXED): DataPath(
                DataLayer.PRODUCTION, DataType.INDEXED, "索引数据"
            ),
            (DataLayer.PRODUCTION, DataType.BACKUPS): DataPath(
                DataLayer.PRODUCTION, DataType.BACKUPS, "备份数据", read_only=True
            ),
            
            # 输出数据层配置
            (DataLayer.OUTPUTS, DataType.REPORTS): DataPath(
                DataLayer.OUTPUTS, DataType.REPORTS, "分析报告"
            ),
            (DataLayer.OUTPUTS, DataType.EXPORTS): DataPath(
                DataLayer.OUTPUTS, DataType.EXPORTS, "导出文件"
            ),
            (DataLayer.OUTPUTS, DataType.VISUALIZATIONS): DataPath(
                DataLayer.OUTPUTS, DataType.VISUALIZATIONS, "可视化结果"
            ),
            (DataLayer.OUTPUTS, DataType.LOGS): DataPath(
                DataLayer.OUTPUTS, DataType.LOGS, "处理日志"
            )
        }
    
    def get_path(self, layer: DataLayer, data_type: DataType) -> Path:
        """获取数据路径"""
        path = self.base_path / layer.value / data_type.value
        
        # 自动创建目录
        config = self.path_config.get((layer, data_type))
        if config and config.auto_create:
            path.mkdir(parents=True, exist_ok=True)
        
        return path
    
    def get_full_path(self, layer: DataLayer, data_type: DataType, filename: str) -> Path:
        """获取完整文件路径"""
        return self.get_path(layer, data_type) / filename
    
    def is_read_only(self, layer: DataLayer, data_type: DataType) -> bool:
        """检查路径是否为只读"""
        config = self.path_config.get((layer, data_type))
        return config.read_only if config else False
    
    def get_description(self, layer: DataLayer, data_type: DataType) -> str:
        """获取路径描述"""
        config = self.path_config.get((layer, data_type))
        return config.description if config else ""
    
    def list_available_paths(self) -> Dict[str, List[str]]:
        """列出所有可用路径"""
        paths_by_layer = {}
        
        for (layer, data_type), config in self.path_config.items():
            layer_name = layer.value
            if layer_name not in paths_by_layer:
                paths_by_layer[layer_name] = []
            
            paths_by_layer[layer_name].append({
                "type": data_type.value,
                "description": config.description,
                "read_only": config.read_only,
                "path": str(self.get_path(layer, data_type))
            })
        
        return paths_by_layer
    
    def validate_structure(self) -> Dict[str, bool]:
        """验证目录结构"""
        validation_results = {}
        
        for (layer, data_type), config in self.path_config.items():
            path = self.get_path(layer, data_type)
            key = f"{layer.value}/{data_type.value}"
            validation_results[key] = path.exists()
        
        return validation_results
    
    def create_missing_directories(self) -> List[str]:
        """创建缺失的目录"""
        created_dirs = []
        
        for (layer, data_type), config in self.path_config.items():
            if config.auto_create:
                path = self.get_path(layer, data_type)
                if not path.exists():
                    path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(str(path))
        
        return created_dirs


# 全局配置实例
unified_data_config = UnifiedDataConfig()


def get_data_path(layer: DataLayer, data_type: DataType) -> Path:
    """获取数据路径的便捷函数"""
    return unified_data_config.get_path(layer, data_type)


def get_equipment_data_path() -> Path:
    """获取设备数据路径"""
    return get_data_path(DataLayer.PRODUCTION, DataType.ACTIVE) / "equipment"


def get_fault_cases_path() -> Path:
    """获取故障案例路径"""
    return get_data_path(DataLayer.ENHANCED, DataType.GENERATED)


def get_knowledge_base_path() -> Path:
    """获取知识库路径"""
    return get_data_path(DataLayer.ENHANCED, DataType.KNOWLEDGE_BASE)


def get_processed_data_path(data_type: DataType = DataType.STRUCTURED) -> Path:
    """获取处理数据路径"""
    return get_data_path(DataLayer.PROCESSED, data_type)


def get_output_reports_path() -> Path:
    """获取输出报告路径"""
    return get_data_path(DataLayer.OUTPUTS, DataType.REPORTS)


def get_cache_path() -> Path:
    """获取缓存路径"""
    return get_data_path(DataLayer.PRODUCTION, DataType.CACHED)


# 兼容性映射 - 用于代码迁移
LEGACY_PATH_MAPPING = {
    "data/01_raw": get_data_path(DataLayer.RAW, DataType.EQUIPMENT),
    "data/02_processed/structured": get_data_path(DataLayer.PROCESSED, DataType.STRUCTURED),
    "data/03_enhanced/generated": get_data_path(DataLayer.ENHANCED, DataType.GENERATED),
    "data/04_production/active/equipment": get_equipment_data_path(),
    "data/04_production/cached": get_data_path(DataLayer.PRODUCTION, DataType.CACHED),
    "data/02_processed/annotated": get_data_path(DataLayer.PROCESSED, DataType.ANNOTATED),
    "data/03_enhanced/integrated": get_data_path(DataLayer.ENHANCED, DataType.INTEGRATED)
}


def get_legacy_path(old_path: str) -> Optional[Path]:
    """获取旧路径对应的新路径"""
    return LEGACY_PATH_MAPPING.get(old_path)
