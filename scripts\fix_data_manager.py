#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复RealDataManager中的data_dir引用问题
"""

import re
import sys
from pathlib import Path

def fix_data_manager():
    """修复RealDataManager中的data_dir引用"""
    app_file = Path("ui/app.py")
    
    if not app_file.exists():
        print("❌ ui/app.py 文件不存在")
        return False
    
    # 读取文件内容
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复模式
    fixes = [
        # 修复故障模式加载
        (
            r"enhanced_patterns_file = os\.path\.join\(self\.data_dir, 'structured', 'enhanced_fault_patterns_baiyin\.json'\)",
            "enhanced_patterns_file = get_processed_data_path(DataType.STRUCTURED) / 'enhanced_fault_patterns_baiyin.json' if self.use_unified_config else os.path.join(self.data_dir, 'structured', 'enhanced_fault_patterns_baiyin.json')"
        ),
        (
            r"if os\.path\.exists\(enhanced_patterns_file\):",
            "if (enhanced_patterns_file.exists() if self.use_unified_config else os.path.exists(enhanced_patterns_file)):"
        ),
        (
            r"basic_patterns_file = os\.path\.join\(self\.data_dir, 'structured', 'fault_patterns_001\.json'\)",
            "basic_patterns_file = get_processed_data_path(DataType.STRUCTURED) / 'fault_patterns_001.json' if self.use_unified_config else os.path.join(self.data_dir, 'structured', 'fault_patterns_001.json')"
        ),
        (
            r"if os\.path\.exists\(basic_patterns_file\):",
            "if (basic_patterns_file.exists() if self.use_unified_config else os.path.exists(basic_patterns_file)):"
        ),
        # 修复设备数据库加载
        (
            r"enhanced_equipment_file = os\.path\.join\(self\.data_dir, 'structured', 'enhanced_equipment_database_baiyin\.json'\)",
            "enhanced_equipment_file = get_processed_data_path(DataType.STRUCTURED) / 'enhanced_equipment_database_baiyin.json' if self.use_unified_config else os.path.join(self.data_dir, 'structured', 'enhanced_equipment_database_baiyin.json')"
        ),
        (
            r"power_stations_file = os\.path\.join\(self\.data_dir, 'structured', 'baiyin_power_stations_20250703\.json'\)",
            "power_stations_file = get_processed_data_path(DataType.STRUCTURED) / 'baiyin_power_stations_20250703.json' if self.use_unified_config else os.path.join(self.data_dir, 'structured', 'baiyin_power_stations_20250703.json')"
        )
    ]
    
    # 应用修复
    modified = False
    for pattern, replacement in fixes:
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
            modified = True
            print(f"✅ 修复: {pattern[:50]}...")
    
    # 如果有修改，写回文件
    if modified:
        with open(app_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ RealDataManager修复完成")
        return True
    else:
        print("⚠️ 没有找到需要修复的内容")
        return False

if __name__ == "__main__":
    fix_data_manager()
