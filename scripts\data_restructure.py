#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据目录重构脚本
重新组织data目录结构，建立统一的数据管理策略
"""

import os
import shutil
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_restructure.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DataRestructureManager:
    """数据重构管理器"""
    
    def __init__(self, base_path: str = "data"):
        self.base_path = Path(base_path)
        self.backup_path = Path(f"data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        # 新的目录结构定义
        self.new_structure = {
            "01_raw": {
                "equipment": "设备原始数据",
                "fault_cases": "故障案例原始数据", 
                "sensor_data": "传感器数据",
                "maintenance_logs": "维护日志",
                "inspection_reports": "巡检报告",
                "uploads": "用户上传文件"
            },
            "02_processed": {
                "cleaned": "清洗后数据",
                "annotated": "标注数据",
                "structured": "结构化数据",
                "validated": "验证后数据"
            },
            "03_enhanced": {
                "generated": "AI生成数据",
                "integrated": "集成数据",
                "knowledge_base": "知识库数据",
                "training_sets": "训练数据集"
            },
            "04_production": {
                "active": "当前使用数据",
                "cached": "缓存数据",
                "indexed": "索引数据",
                "backups": "备份数据"
            },
            "05_outputs": {
                "reports": "分析报告",
                "exports": "导出文件",
                "visualizations": "可视化结果",
                "logs": "处理日志"
            },
            "metadata": {
                "schemas": "数据模式定义",
                "catalogs": "数据目录",
                "lineage": "数据血缘",
                "quality": "数据质量报告"
            }
        }
        
        # 数据迁移映射
        self.migration_mapping = {
            # 原始数据迁移
            "raw/equipment_photo_001.txt": "01_raw/equipment/equipment_photo_001.txt",
            "raw/fault_cases": "01_raw/fault_cases",
            "raw/sensor_data": "01_raw/sensor_data", 
            "raw/maintenance_log_001.json": "01_raw/maintenance_logs/maintenance_log_001.json",
            "raw/inspection_checklist_001.txt": "01_raw/inspection_reports/inspection_checklist_001.txt",
            "raw/uploads": "01_raw/uploads",
            "raw": "01_raw",
            
            # 处理数据迁移
            "annotations": "02_processed/annotated",
            "structured": "02_processed/structured",
            "cleaned": "02_processed/cleaned",
            
            # 增强数据迁移
            "generated": "03_enhanced/generated",
            "massive_generated": "03_enhanced/generated/advanced",
            "integrated": "03_enhanced/integrated",
            
            # 生产数据迁移
            "equipment": "04_production/active/equipment",
            "processed": "04_production/cached",
            
            # 输出数据迁移 - 按文件类型分类
            "processed/*.md": "05_outputs/reports",
            "processed/*.json": "05_outputs/logs"
        }
    
    def create_backup(self) -> bool:
        """创建数据备份"""
        try:
            logger.info(f"创建数据备份到: {self.backup_path}")
            if self.base_path.exists():
                shutil.copytree(self.base_path, self.backup_path)
                logger.info("数据备份完成")
                return True
            else:
                logger.warning(f"源目录不存在: {self.base_path}")
                return False
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return False
    
    def create_new_structure(self) -> bool:
        """创建新的目录结构"""
        try:
            logger.info("创建新的目录结构")
            
            for main_dir, sub_dirs in self.new_structure.items():
                main_path = self.base_path / main_dir
                main_path.mkdir(parents=True, exist_ok=True)
                
                for sub_dir, description in sub_dirs.items():
                    sub_path = main_path / sub_dir
                    sub_path.mkdir(parents=True, exist_ok=True)
                    
                    # 创建README文件说明目录用途
                    readme_path = sub_path / "README.md"
                    with open(readme_path, 'w', encoding='utf-8') as f:
                        f.write(f"# {sub_dir}\n\n{description}\n\n")
                        f.write(f"创建时间: {datetime.now().isoformat()}\n")
                
                logger.info(f"创建目录: {main_dir}")
            
            logger.info("新目录结构创建完成")
            return True
            
        except Exception as e:
            logger.error(f"创建新目录结构失败: {e}")
            return False
    
    def migrate_data(self) -> bool:
        """执行数据迁移"""
        try:
            logger.info("开始数据迁移")
            migration_log = []
            
            for old_path, new_path in self.migration_mapping.items():
                old_full_path = self.base_path / old_path
                new_full_path = self.base_path / new_path
                
                if "*" in old_path:
                    # 处理通配符路径
                    self._migrate_wildcard_files(old_path, new_path, migration_log)
                elif old_full_path.exists():
                    # 确保目标目录存在
                    new_full_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    if old_full_path.is_dir():
                        if new_full_path.exists():
                            shutil.rmtree(new_full_path)
                        shutil.copytree(old_full_path, new_full_path)
                    else:
                        shutil.copy2(old_full_path, new_full_path)
                    
                    migration_log.append({
                        "source": str(old_path),
                        "target": str(new_path),
                        "type": "directory" if old_full_path.is_dir() else "file",
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    logger.info(f"迁移: {old_path} → {new_path}")
                else:
                    logger.warning(f"源路径不存在: {old_path}")
            
            # 保存迁移日志
            self._save_migration_log(migration_log)
            logger.info("数据迁移完成")
            return True
            
        except Exception as e:
            logger.error(f"数据迁移失败: {e}")
            return False
    
    def _migrate_wildcard_files(self, pattern: str, target_dir: str, migration_log: List[Dict]):
        """迁移通配符匹配的文件"""
        try:
            base_dir, file_pattern = pattern.split("*")
            base_path = self.base_path / base_dir.rstrip("/")
            target_path = self.base_path / target_dir
            
            if not base_path.exists():
                return
            
            target_path.mkdir(parents=True, exist_ok=True)
            
            for file_path in base_path.glob(f"*{file_pattern}"):
                target_file = target_path / file_path.name
                shutil.copy2(file_path, target_file)
                
                migration_log.append({
                    "source": str(file_path.relative_to(self.base_path)),
                    "target": str(target_file.relative_to(self.base_path)),
                    "type": "file",
                    "timestamp": datetime.now().isoformat()
                })
                
                logger.info(f"迁移文件: {file_path.name} → {target_dir}")
                
        except Exception as e:
            logger.error(f"通配符文件迁移失败: {e}")
    
    def _save_migration_log(self, migration_log: List[Dict]):
        """保存迁移日志"""
        try:
            log_path = self.base_path / "metadata" / "lineage" / "migration_log.json"
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(log_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "migration_timestamp": datetime.now().isoformat(),
                    "total_items": len(migration_log),
                    "migrations": migration_log
                }, f, indent=2, ensure_ascii=False)
                
            logger.info(f"迁移日志保存到: {log_path}")
            
        except Exception as e:
            logger.error(f"保存迁移日志失败: {e}")
    
    def cleanup_old_structure(self) -> bool:
        """清理旧的目录结构"""
        try:
            logger.info("清理旧目录结构")
            
            old_dirs = [
                "annotations", "cleaned", "equipment", "generated", 
                "integrated", "massive_generated", "processed", 
                "professional_processed", "raw", "structured",
                "test_cleaned", "test_professional"
            ]
            
            for old_dir in old_dirs:
                old_path = self.base_path / old_dir
                if old_path.exists():
                    shutil.rmtree(old_path)
                    logger.info(f"删除旧目录: {old_dir}")
            
            logger.info("旧目录结构清理完成")
            return True
            
        except Exception as e:
            logger.error(f"清理旧目录结构失败: {e}")
            return False
    
    def generate_structure_report(self) -> Dict:
        """生成目录结构报告"""
        try:
            report = {
                "restructure_timestamp": datetime.now().isoformat(),
                "new_structure": {},
                "statistics": {
                    "total_directories": 0,
                    "total_files": 0,
                    "total_size_mb": 0
                }
            }
            
            for main_dir in self.new_structure.keys():
                main_path = self.base_path / main_dir
                if main_path.exists():
                    dir_info = self._analyze_directory(main_path)
                    report["new_structure"][main_dir] = dir_info
                    
                    report["statistics"]["total_directories"] += dir_info["subdirectories"]
                    report["statistics"]["total_files"] += dir_info["files"]
                    report["statistics"]["total_size_mb"] += dir_info["size_mb"]
            
            # 保存报告
            report_path = self.base_path / "metadata" / "quality" / "structure_report.json"
            report_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"目录结构报告保存到: {report_path}")
            return report
            
        except Exception as e:
            logger.error(f"生成目录结构报告失败: {e}")
            return {}
    
    def _analyze_directory(self, path: Path) -> Dict:
        """分析目录信息"""
        try:
            total_files = 0
            total_size = 0
            subdirs = 0
            
            for item in path.rglob("*"):
                if item.is_file():
                    total_files += 1
                    total_size += item.stat().st_size
                elif item.is_dir():
                    subdirs += 1
            
            return {
                "path": str(path.relative_to(self.base_path)),
                "files": total_files,
                "subdirectories": subdirs,
                "size_mb": round(total_size / (1024 * 1024), 2)
            }
            
        except Exception as e:
            logger.error(f"分析目录失败: {e}")
            return {"error": str(e)}


def main():
    """主函数"""
    logger.info("开始数据目录重构")
    
    # 创建重构管理器
    restructure_manager = DataRestructureManager()
    
    # 步骤1: 创建备份
    if not restructure_manager.create_backup():
        logger.error("备份失败，终止重构")
        return False
    
    # 步骤2: 创建新目录结构
    if not restructure_manager.create_new_structure():
        logger.error("创建新目录结构失败")
        return False
    
    # 步骤3: 数据迁移
    if not restructure_manager.migrate_data():
        logger.error("数据迁移失败")
        return False
    
    # 步骤4: 生成报告
    report = restructure_manager.generate_structure_report()
    if report:
        logger.info(f"重构完成，共处理 {report['statistics']['total_files']} 个文件")
    
    # 步骤5: 清理旧结构（可选，需要确认）
    confirm = input("是否清理旧的目录结构？(y/N): ")
    if confirm.lower() == 'y':
        restructure_manager.cleanup_old_structure()
    
    logger.info("数据目录重构完成")
    return True


class UnifiedDataManager:
    """统一数据管理器 - 基于新的目录结构"""

    def __init__(self, base_path: str = "data"):
        self.base_path = Path(base_path)
        self.data_paths = {
            "raw": self.base_path / "01_raw",
            "processed": self.base_path / "02_processed",
            "enhanced": self.base_path / "03_enhanced",
            "production": self.base_path / "04_production",
            "outputs": self.base_path / "05_outputs",
            "metadata": self.base_path / "metadata"
        }

    def get_equipment_data(self) -> Dict:
        """获取设备数据"""
        equipment_path = self.data_paths["production"] / "active" / "equipment"
        if equipment_path.exists():
            return self._load_json_files(equipment_path)
        return {}

    def get_fault_cases(self) -> List[Dict]:
        """获取故障案例"""
        cases_path = self.data_paths["enhanced"] / "generated"
        fault_cases = []

        for file_path in cases_path.glob("*fault_cases*.json"):
            data = self._load_json_file(file_path)
            if isinstance(data, list):
                fault_cases.extend(data)
            elif isinstance(data, dict):
                fault_cases.append(data)

        return fault_cases

    def get_knowledge_base(self) -> Dict:
        """获取知识库数据"""
        kb_path = self.data_paths["enhanced"] / "knowledge_base"
        if not kb_path.exists():
            kb_path.mkdir(parents=True, exist_ok=True)

        return self._load_json_files(kb_path)

    def save_processed_data(self, data: Dict, filename: str, data_type: str = "structured"):
        """保存处理后的数据"""
        target_path = self.data_paths["processed"] / data_type / filename
        target_path.parent.mkdir(parents=True, exist_ok=True)

        with open(target_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    def save_output_report(self, content: str, filename: str):
        """保存输出报告"""
        report_path = self.data_paths["outputs"] / "reports" / filename
        report_path.parent.mkdir(parents=True, exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def _load_json_file(self, file_path: Path) -> Dict:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载JSON文件失败 {file_path}: {e}")
            return {}

    def _load_json_files(self, directory: Path) -> Dict:
        """加载目录下所有JSON文件"""
        data = {}
        if not directory.exists():
            return data

        for file_path in directory.glob("*.json"):
            file_data = self._load_json_file(file_path)
            data[file_path.stem] = file_data

        return data


if __name__ == "__main__":
    main()
