#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的专业系统质量
"""

import requests
import json
import time


def test_professional_system():
    print('🧪 测试重构后的专业系统质量...')
    
    base_url = 'http://localhost:5002'
    
    # 测试用例
    test_cases = [
        {
            'query': '110kV变压器套管渗油故障的深度分析和处理方案',
            'description': '专业故障分析测试'
        },
        {
            'query': 'SF6断路器操作机构拒动的故障机理分析',
            'description': '设备故障机理测试'
        }
    ]
    
    results = {'r1_results': [], 'v3_results': [], 'improvements': []}
    
    for i, test_case in enumerate(test_cases):
        query = test_case['query']
        desc = test_case['description']
        print(f'\n📋 测试用例 {i+1}: {desc}')
        print(f'   查询: {query[:50]}...')
        
        # 测试DeepSeek-R1
        try:
            print('   🧠 测试DeepSeek-R1...')
            response = requests.post(
                f'{base_url}/api/v1/analyze',
                json={'query': query, 'model': 'deepseek-r1'},
                timeout=120
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    # R1返回final_analysis字段，V3返回analysis字段
                    analysis_content = data.get('final_analysis', '') or data.get('analysis', '')
                    r1_result = {
                        'success': True,
                        'content_length': len(analysis_content),
                        'has_thinking': data.get('has_thinking', False),
                        'context_used': data.get('context_used', False),
                        'response_time': response.elapsed.total_seconds()
                    }
                    content_len = r1_result['content_length']
                    has_thinking = r1_result['has_thinking']
                    resp_time = r1_result['response_time']
                    print(f'      ✅ R1成功: 内容长度={content_len}, 推理过程={has_thinking}, 响应时间={resp_time:.1f}s')
                else:
                    r1_result = {'success': False, 'error': data.get('error', '未知错误')}
                    print(f'      ❌ R1失败: {r1_result["error"]}')
            else:
                r1_result = {'success': False, 'error': f'HTTP {response.status_code}'}
                print(f'      ❌ R1失败: HTTP {response.status_code}')
                
        except Exception as e:
            r1_result = {'success': False, 'error': str(e)}
            print(f'      ❌ R1异常: {str(e)}')
        
        results['r1_results'].append(r1_result)
        
        # 测试DeepSeek-V3
        try:
            print('   🧠 测试DeepSeek-V3...')
            response = requests.post(
                f'{base_url}/api/v1/analyze',
                json={'query': query, 'model': 'deepseek-v3'},
                timeout=120
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    v3_result = {
                        'success': True,
                        'content_length': len(data.get('analysis', '')),
                        'context_used': data.get('context_used', False),
                        'response_time': response.elapsed.total_seconds()
                    }
                    content_len = v3_result['content_length']
                    context_used = v3_result['context_used']
                    resp_time = v3_result['response_time']
                    print(f'      ✅ V3成功: 内容长度={content_len}, 上下文使用={context_used}, 响应时间={resp_time:.1f}s')
                else:
                    v3_result = {'success': False, 'error': data.get('error', '未知错误')}
                    print(f'      ❌ V3失败: {v3_result["error"]}')
            else:
                v3_result = {'success': False, 'error': f'HTTP {response.status_code}'}
                print(f'      ❌ V3失败: HTTP {response.status_code}')
                
        except Exception as e:
            v3_result = {'success': False, 'error': str(e)}
            print(f'      ❌ V3异常: {str(e)}')
        
        results['v3_results'].append(v3_result)
        
        # 分析改进效果
        improvements = []
        if r1_result.get('success') and r1_result.get('content_length', 0) > 500:
            improvements.append('R1内容详实')
        if v3_result.get('success') and v3_result.get('content_length', 0) > 500:
            improvements.append('V3内容详实')
        if r1_result.get('context_used') or v3_result.get('context_used'):
            improvements.append('使用专业上下文')
        
        results['improvements'].append(improvements)
        improvement_text = ', '.join(improvements) if improvements else '无明显改进'
        print(f'   📈 改进点: {improvement_text}')
    
    # 计算总体改进效果
    r1_success_rate = sum(1 for r in results['r1_results'] if r.get('success')) / len(results['r1_results'])
    v3_success_rate = sum(1 for r in results['v3_results'] if r.get('success')) / len(results['v3_results'])
    
    r1_successful = [r for r in results['r1_results'] if r.get('success')]
    v3_successful = [r for r in results['v3_results'] if r.get('success')]
    
    avg_r1_length = sum(r.get('content_length', 0) for r in r1_successful) / max(len(r1_successful), 1)
    avg_v3_length = sum(r.get('content_length', 0) for r in v3_successful) / max(len(v3_successful), 1)
    
    print(f'\n📊 专业系统质量测试结果:')
    print(f'   R1成功率: {r1_success_rate*100:.1f}%')
    print(f'   V3成功率: {v3_success_rate*100:.1f}%')
    print(f'   R1平均内容长度: {avg_r1_length:.0f} 字符')
    print(f'   V3平均内容长度: {avg_v3_length:.0f} 字符')
    
    overall_success = (r1_success_rate + v3_success_rate) / 2
    if overall_success >= 0.8:
        print(f'🎉 专业系统质量: 优秀 ({overall_success*100:.1f}%)')
    elif overall_success >= 0.6:
        print(f'✅ 专业系统质量: 良好 ({overall_success*100:.1f}%)')
    else:
        print(f'⚠️ 专业系统质量: 需要改进 ({overall_success*100:.1f}%)')
    
    return results


def test_retrieval_quality():
    """测试检索质量"""
    print('\n🔍 测试检索系统质量...')
    
    base_url = 'http://localhost:5002'
    
    try:
        response = requests.post(
            f'{base_url}/api/v1/knowledge/search',
            json={'query': '变压器故障', 'search_type': 'comprehensive'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            
            if results:
                avg_relevance = sum(r.get('relevance_score', 0) for r in results) / len(results)
                high_quality_count = sum(1 for r in results if r.get('relevance_score', 0) > 0.7)
                
                print(f'   ✅ 检索成功: {len(results)} 个结果')
                print(f'   📊 平均相关性: {avg_relevance:.3f}')
                print(f'   🎯 高质量结果: {high_quality_count}/{len(results)}')
                
                return {
                    'success': True,
                    'result_count': len(results),
                    'avg_relevance': avg_relevance,
                    'high_quality_ratio': high_quality_count / len(results)
                }
            else:
                print('   ⚠️ 检索结果为空')
                return {'success': False, 'error': '无检索结果'}
        else:
            print(f'   ❌ 检索失败: HTTP {response.status_code}')
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
    except Exception as e:
        print(f'   ❌ 检索异常: {str(e)}')
        return {'success': False, 'error': str(e)}


def main():
    """主测试函数"""
    print('🚀 开始专业系统质量综合测试')
    print('='*60)
    
    # 测试分析质量
    analysis_results = test_professional_system()
    
    # 测试检索质量
    retrieval_results = test_retrieval_quality()
    
    # 综合评估
    print('\n🎯 综合质量评估:')
    
    analysis_success = (
        sum(1 for r in analysis_results['r1_results'] if r.get('success')) +
        sum(1 for r in analysis_results['v3_results'] if r.get('success'))
    ) / (len(analysis_results['r1_results']) + len(analysis_results['v3_results']))
    
    retrieval_success = 1.0 if retrieval_results.get('success') else 0.0
    
    overall_quality = (analysis_success * 0.7 + retrieval_success * 0.3)
    
    print(f'   分析系统质量: {analysis_success*100:.1f}%')
    print(f'   检索系统质量: {retrieval_success*100:.1f}%')
    print(f'   综合系统质量: {overall_quality*100:.1f}%')
    
    if overall_quality >= 0.8:
        print('🎉 系统质量评级: 优秀 - 已达到专业级标准!')
    elif overall_quality >= 0.6:
        print('✅ 系统质量评级: 良好 - 基本达到专业要求')
    else:
        print('⚠️ 系统质量评级: 需要改进 - 距离专业标准还有差距')
    
    return {
        'analysis_results': analysis_results,
        'retrieval_results': retrieval_results,
        'overall_quality': overall_quality
    }


if __name__ == '__main__':
    main()
