# 处理后数据目录

此目录用于存储经过预处理的数据文件。

## 目录结构

```
data/04_production/cached/
├── text/                  # 处理后的文本数据
│   ├── ocr_results/      # OCR识别结果
│   ├── cleaned_text/     # 清洗后的文本
│   └── extracted_info/   # 提取的信息
├── images/               # 处理后的图像数据
│   ├── resized/         # 调整尺寸后的图像
│   ├── enhanced/        # 增强后的图像
│   └── annotated/       # 标注后的图像
├── vectors/             # 向量化数据
│   ├── text_embeddings/ # 文本向量
│   └── image_features/  # 图像特征
└── metadata/            # 元数据信息
    ├── file_info.json   # 文件信息
    └── processing_log.json # 处理日志
```

## 数据处理流程

1. **文本处理**
   - OCR文字识别
   - 文本清洗和标准化
   - 关键信息提取
   - 文本向量化

2. **图像处理**
   - 图像尺寸调整
   - 图像质量增强
   - 缺陷检测标注
   - 特征提取

3. **数据向量化**
   - 使用预训练模型生成向量
   - 向量标准化处理
   - 相似度计算准备

## 文件命名规范

- 处理后文件保持原文件名，添加处理类型后缀
- 格式：`原文件名_处理类型.扩展名`
- 示例：`report_001_ocr.txt`, `image_001_enhanced.jpg`

## 数据格式说明

### 文本数据格式
```json
{
  "source_file": "原始文件路径",
  "processed_time": "处理时间",
  "content": "处理后内容",
  "metadata": {
    "confidence": 0.95,
    "language": "zh-cn",
    "word_count": 1234
  }
}
```

### 图像数据格式
```json
{
  "source_file": "原始文件路径", 
  "processed_time": "处理时间",
  "image_path": "处理后图像路径",
  "metadata": {
    "width": 1920,
    "height": 1080,
    "format": "JPEG",
    "quality": 85
  }
}
```

## 清理策略

- 定期清理超过30天的临时处理文件
- 保留重要的处理结果和元数据
- 压缩存档长期不用的数据
