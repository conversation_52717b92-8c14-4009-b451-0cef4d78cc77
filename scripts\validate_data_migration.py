#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移验证脚本
验证数据目录重构后的完整性和正确性
"""

import os
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataMigrationValidator:
    """数据迁移验证器"""
    
    def __init__(self, data_path: str = "data", backup_path: str = None):
        self.data_path = Path(data_path)
        self.backup_path = Path(backup_path) if backup_path else None
        
        # 预期的新目录结构
        self.expected_structure = {
            "01_raw": ["equipment", "fault_cases", "sensor_data", "maintenance_logs", "inspection_reports", "uploads"],
            "02_processed": ["cleaned", "annotated", "structured", "validated"],
            "03_enhanced": ["generated", "integrated", "knowledge_base", "training_sets"],
            "04_production": ["active", "cached", "indexed", "backups"],
            "05_outputs": ["reports", "exports", "visualizations", "logs"],
            "metadata": ["schemas", "catalogs", "lineage", "quality"]
        }
    
    def validate_directory_structure(self) -> Dict[str, bool]:
        """验证目录结构"""
        logger.info("验证目录结构...")
        validation_results = {}
        
        for main_dir, sub_dirs in self.expected_structure.items():
            main_path = self.data_path / main_dir
            validation_results[main_dir] = main_path.exists()
            
            if main_path.exists():
                for sub_dir in sub_dirs:
                    sub_path = main_path / sub_dir
                    validation_results[f"{main_dir}/{sub_dir}"] = sub_path.exists()
            else:
                logger.warning(f"主目录不存在: {main_dir}")
        
        return validation_results
    
    def validate_data_integrity(self) -> Dict[str, any]:
        """验证数据完整性"""
        logger.info("验证数据完整性...")
        
        integrity_report = {
            "total_files": 0,
            "total_size_mb": 0,
            "file_types": {},
            "corrupted_files": [],
            "empty_directories": [],
            "large_files": []  # 大于100MB的文件
        }
        
        for file_path in self.data_path.rglob("*"):
            if file_path.is_file():
                try:
                    # 统计文件
                    integrity_report["total_files"] += 1
                    file_size = file_path.stat().st_size
                    integrity_report["total_size_mb"] += file_size / (1024 * 1024)
                    
                    # 统计文件类型
                    file_ext = file_path.suffix.lower()
                    if file_ext not in integrity_report["file_types"]:
                        integrity_report["file_types"][file_ext] = 0
                    integrity_report["file_types"][file_ext] += 1
                    
                    # 检查大文件
                    if file_size > 100 * 1024 * 1024:  # 100MB
                        integrity_report["large_files"].append({
                            "path": str(file_path.relative_to(self.data_path)),
                            "size_mb": round(file_size / (1024 * 1024), 2)
                        })
                    
                    # 验证JSON文件格式
                    if file_ext == '.json':
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                json.load(f)
                        except json.JSONDecodeError:
                            integrity_report["corrupted_files"].append(str(file_path.relative_to(self.data_path)))
                
                except Exception as e:
                    logger.error(f"处理文件失败 {file_path}: {e}")
                    integrity_report["corrupted_files"].append(str(file_path.relative_to(self.data_path)))
            
            elif file_path.is_dir():
                # 检查空目录
                if not any(file_path.iterdir()):
                    integrity_report["empty_directories"].append(str(file_path.relative_to(self.data_path)))
        
        integrity_report["total_size_mb"] = round(integrity_report["total_size_mb"], 2)
        return integrity_report
    
    def validate_data_migration(self) -> Dict[str, any]:
        """验证数据迁移完整性"""
        if not self.backup_path or not self.backup_path.exists():
            logger.warning("备份路径不存在，跳过迁移验证")
            return {"status": "skipped", "reason": "no_backup"}
        
        logger.info("验证数据迁移完整性...")
        
        migration_report = {
            "backup_files": 0,
            "migrated_files": 0,
            "missing_files": [],
            "size_comparison": {
                "backup_size_mb": 0,
                "current_size_mb": 0,
                "difference_mb": 0
            },
            "file_hash_mismatches": []
        }
        
        # 统计备份文件
        backup_files = {}
        for file_path in self.backup_path.rglob("*"):
            if file_path.is_file():
                migration_report["backup_files"] += 1
                rel_path = file_path.relative_to(self.backup_path)
                backup_files[str(rel_path)] = {
                    "size": file_path.stat().st_size,
                    "hash": self._calculate_file_hash(file_path)
                }
                migration_report["size_comparison"]["backup_size_mb"] += file_path.stat().st_size / (1024 * 1024)
        
        # 统计当前文件
        current_files = {}
        for file_path in self.data_path.rglob("*"):
            if file_path.is_file():
                migration_report["migrated_files"] += 1
                rel_path = file_path.relative_to(self.data_path)
                current_files[str(rel_path)] = {
                    "size": file_path.stat().st_size,
                    "hash": self._calculate_file_hash(file_path)
                }
                migration_report["size_comparison"]["current_size_mb"] += file_path.stat().st_size / (1024 * 1024)
        
        # 计算差异
        migration_report["size_comparison"]["backup_size_mb"] = round(migration_report["size_comparison"]["backup_size_mb"], 2)
        migration_report["size_comparison"]["current_size_mb"] = round(migration_report["size_comparison"]["current_size_mb"], 2)
        migration_report["size_comparison"]["difference_mb"] = round(
            migration_report["size_comparison"]["current_size_mb"] - migration_report["size_comparison"]["backup_size_mb"], 2
        )
        
        return migration_report
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return ""
    
    def validate_code_compatibility(self) -> Dict[str, any]:
        """验证代码兼容性"""
        logger.info("验证代码兼容性...")
        
        compatibility_report = {
            "files_to_update": [],
            "old_path_references": [],
            "potential_issues": []
        }
        
        # 需要检查的文件类型
        code_files = []
        for ext in ['.py', '.js', '.html', '.json', '.yaml', '.yml']:
            code_files.extend(Path('.').rglob(f"*{ext}"))
        
        # 旧路径模式
        old_path_patterns = [
            "data/01_raw", "data/02_processed/structured", "data/03_enhanced/generated", "data/04_production/active/equipment",
            "data/04_production/cached", "data/02_processed/annotated", "data/03_enhanced/integrated", "data/02_processed/cleaned"
        ]
        
        for file_path in code_files:
            if file_path.is_file() and 'data_backup' not in str(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        for old_path in old_path_patterns:
                            if old_path in content:
                                compatibility_report["files_to_update"].append(str(file_path))
                                compatibility_report["old_path_references"].append({
                                    "file": str(file_path),
                                    "old_path": old_path,
                                    "line_count": content.count(old_path)
                                })
                                break
                
                except Exception as e:
                    compatibility_report["potential_issues"].append({
                        "file": str(file_path),
                        "error": str(e)
                    })
        
        return compatibility_report
    
    def generate_validation_report(self) -> Dict[str, any]:
        """生成完整的验证报告"""
        logger.info("生成验证报告...")
        
        report = {
            "validation_timestamp": datetime.now().isoformat(),
            "directory_structure": self.validate_directory_structure(),
            "data_integrity": self.validate_data_integrity(),
            "migration_validation": self.validate_data_migration(),
            "code_compatibility": self.validate_code_compatibility()
        }
        
        # 计算总体评分
        structure_score = sum(1 for v in report["directory_structure"].values() if v) / len(report["directory_structure"])
        integrity_score = 1.0 - (len(report["data_integrity"]["corrupted_files"]) / max(report["data_integrity"]["total_files"], 1))
        compatibility_score = 1.0 - (len(report["code_compatibility"]["files_to_update"]) / max(len(report["code_compatibility"]["files_to_update"]) + 10, 10))
        
        report["overall_score"] = {
            "structure_score": round(structure_score * 100, 1),
            "integrity_score": round(integrity_score * 100, 1),
            "compatibility_score": round(compatibility_score * 100, 1),
            "total_score": round((structure_score + integrity_score + compatibility_score) / 3 * 100, 1)
        }
        
        # 保存报告
        report_path = self.data_path / "metadata" / "quality" / "migration_validation_report.json"
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"验证报告保存到: {report_path}")
        return report
    
    def print_summary(self, report: Dict[str, any]):
        """打印验证摘要"""
        print("\n" + "="*60)
        print("数据迁移验证报告摘要")
        print("="*60)
        
        print(f"📊 总体评分: {report['overall_score']['total_score']}%")
        print(f"   - 目录结构: {report['overall_score']['structure_score']}%")
        print(f"   - 数据完整性: {report['overall_score']['integrity_score']}%")
        print(f"   - 代码兼容性: {report['overall_score']['compatibility_score']}%")
        
        print(f"\n📁 数据统计:")
        print(f"   - 总文件数: {report['data_integrity']['total_files']}")
        print(f"   - 总大小: {report['data_integrity']['total_size_mb']} MB")
        print(f"   - 损坏文件: {len(report['data_integrity']['corrupted_files'])}")
        print(f"   - 空目录: {len(report['data_integrity']['empty_directories'])}")
        
        print(f"\n🔧 代码更新:")
        print(f"   - 需要更新的文件: {len(report['code_compatibility']['files_to_update'])}")
        print(f"   - 旧路径引用: {len(report['code_compatibility']['old_path_references'])}")
        
        if report['overall_score']['total_score'] >= 90:
            print("\n✅ 数据迁移验证通过！")
        elif report['overall_score']['total_score'] >= 70:
            print("\n⚠️ 数据迁移基本成功，但需要注意一些问题")
        else:
            print("\n❌ 数据迁移存在严重问题，需要修复")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="数据迁移验证工具")
    parser.add_argument("--data-path", default="data", help="数据目录路径")
    parser.add_argument("--backup-path", help="备份目录路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建验证器
    validator = DataMigrationValidator(args.data_path, args.backup_path)
    
    # 生成验证报告
    report = validator.generate_validation_report()
    
    # 打印摘要
    validator.print_summary(report)


if __name__ == "__main__":
    main()
