#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chroma向量数据库管理器
专门用于管理白银电力系统的向量数据库
使用现代化的ChromaDB API
"""

from typing import Dict, List, Any, Optional
from loguru import logger

try:
    from .modern_chroma_manager import ModernChromaManager, get_modern_chroma_manager
    CHROMA_AVAILABLE = True
except ImportError:
    try:
        from modern_chroma_manager import ModernChromaManager, get_modern_chroma_manager
        CHROMA_AVAILABLE = True
    except ImportError:
        CHROMA_AVAILABLE = False
        logger.error("Chroma未安装，请运行: pip install chromadb")


class ChromaManager:
    """Chroma向量数据库管理器 - 白银电力系统专用（现代化版本）"""
    
    def __init__(self, config: Dict[str, Any] = None):
        if not CHROMA_AVAILABLE:
            raise ImportError("Chroma未安装，无法使用ChromaManager")
        
        # 使用现代化的Chroma管理器
        try:
            self.modern_manager = get_modern_chroma_manager()
            logger.info("✅ 使用现代化Chroma管理器")
        except Exception as e:
            logger.error(f"❌ 现代化Chroma管理器初始化失败: {e}")
            self.modern_manager = None
        
        self.config = config or {}
        
        # 为了兼容性，保留一些属性
        self.persist_directory = self.config.get("persist_directory", "./embeddings/chroma_store")
        self.collection_name = self.config.get("collection_name", "baiyin_power_fault_collection")

    def is_available(self) -> bool:
        """检查Chroma是否可用"""
        return self.modern_manager and self.modern_manager.is_available()
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档到集合"""
        if not self.modern_manager:
            logger.error("现代化Chroma管理器不可用")
            return False
        
        return self.modern_manager.add_documents(documents)
    
    def search(self, query: str, limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """搜索文档"""
        if not self.modern_manager:
            logger.error("现代化Chroma管理器不可用")
            return []
        
        return self.modern_manager.search(query, limit, **kwargs)
    
    def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        if not self.modern_manager:
            return {"error": "现代化Chroma管理器不可用"}
        
        return self.modern_manager.get_collection_info()
    
    def delete_documents(self, document_ids: List[str]) -> bool:
        """删除文档"""
        if not self.modern_manager:
            logger.error("现代化Chroma管理器不可用")
            return False
        
        return self.modern_manager.delete_documents(document_ids)
    
    def update_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """更新文档"""
        if not self.modern_manager:
            logger.error("现代化Chroma管理器不可用")
            return False
        
        return self.modern_manager.update_documents(documents)
    
    def clear_collection(self) -> bool:
        """清空集合"""
        if not self.modern_manager:
            logger.error("现代化Chroma管理器不可用")
            return False
        
        return self.modern_manager.clear_collection()
    
    def get_document_count(self) -> int:
        """获取文档数量"""
        if not self.modern_manager:
            return 0
        
        return self.modern_manager.get_document_count()
    
    def batch_add_documents(self, documents: List[Dict[str, Any]], batch_size: int = 100) -> bool:
        """批量添加文档"""
        if not self.modern_manager:
            logger.error("现代化Chroma管理器不可用")
            return False
        
        return self.modern_manager.batch_add_documents(documents, batch_size)


def get_chroma_manager(config: Dict[str, Any] = None) -> Optional[ChromaManager]:
    """获取Chroma管理器实例"""
    try:
        return ChromaManager(config)
    except ImportError:
        logger.error("无法创建Chroma管理器，请检查依赖")
        return None


# 全局实例
_global_chroma_manager = None

def get_global_chroma_manager() -> Optional[ChromaManager]:
    """获取全局Chroma管理器实例"""
    global _global_chroma_manager
    if _global_chroma_manager is None:
        _global_chroma_manager = get_chroma_manager()
    return _global_chroma_manager
