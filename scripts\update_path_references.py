#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码路径引用更新脚本
自动更新代码中的旧数据路径引用为新的统一数据路径
"""

import os
import re
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PathReferenceUpdater:
    """路径引用更新器"""
    
    def __init__(self):
        # 路径映射规则
        self.path_mappings = {
            # 原始数据路径映射
            r'data/01_raw(?!/\w)': 'data/01_raw',
            r'data/01_raw/': 'data/01_raw/',
            
            # 处理数据路径映射
            r'data/02_processed/structured(?!/\w)': 'data/02_processed/structured',
            r'data/02_processed/structured/': 'data/02_processed/structured/',
            r'data/02_processed/cleaned(?!/\w)': 'data/02_processed/cleaned',
            r'data/02_processed/cleaned/': 'data/02_processed/cleaned/',
            r'data/02_processed/annotated(?!/\w)': 'data/02_processed/annotated',
            r'data/02_processed/annotated/': 'data/02_processed/annotated/',
            
            # 增强数据路径映射
            r'data/03_enhanced/generated(?!/\w)': 'data/03_enhanced/generated',
            r'data/03_enhanced/generated/': 'data/03_enhanced/generated/',
            r'data/03_enhanced/generated/advanced(?!/\w)': 'data/03_enhanced/generated/advanced',
            r'data/03_enhanced/generated/advanced/': 'data/03_enhanced/generated/advanced/',
            r'data/03_enhanced/integrated(?!/\w)': 'data/03_enhanced/integrated',
            r'data/03_enhanced/integrated/': 'data/03_enhanced/integrated/',
            
            # 生产数据路径映射
            r'data/04_production/active/equipment(?!/\w)': 'data/04_production/active/equipment',
            r'data/04_production/active/equipment/': 'data/04_production/active/equipment/',
            r'data/04_production/cached(?!/\w)': 'data/04_production/cached',
            r'data/04_production/cached/': 'data/04_production/cached/',
            
            # Python路径分隔符版本
            r'data\\\\raw(?!\\\\\\w)': 'data\\\\01_raw',
            r'data\\\\raw\\\\': 'data\\\\01_raw\\\\',
            r'data\\\\structured(?!\\\\\\w)': 'data\\\\02_processed\\\\structured',
            r'data\\\\structured\\\\': 'data\\\\02_processed\\\\structured\\\\',
            r'data\\\\generated(?!\\\\\\w)': 'data\\\\03_enhanced\\\\generated',
            r'data\\\\generated\\\\': 'data\\\\03_enhanced\\\\generated\\\\',
            r'data\\\\equipment(?!\\\\\\w)': 'data\\\\04_production\\\\active\\\\equipment',
            r'data\\\\equipment\\\\': 'data\\\\04_production\\\\active\\\\equipment\\\\',
            r'data\\\\processed(?!\\\\\\w)': 'data\\\\04_production\\\\cached',
            r'data\\\\processed\\\\': 'data\\\\04_production\\\\cached\\\\',
        }
        
        # 需要更新的文件类型
        self.file_extensions = ['.py', '.js', '.html', '.json', '.yaml', '.yml', '.md', '.txt']
        
        # 排除的目录
        self.exclude_dirs = {
            '.git', '.venv', '__pycache__', 'node_modules', 
            'data_backup_*', '.pytest_cache', '.mypy_cache'
        }
        
        # 更新统计
        self.update_stats = {
            'files_processed': 0,
            'files_updated': 0,
            'total_replacements': 0,
            'errors': []
        }
    
    def should_process_file(self, file_path: Path) -> bool:
        """判断是否应该处理该文件"""
        # 检查文件扩展名
        if file_path.suffix not in self.file_extensions:
            return False
        
        # 检查是否在排除目录中
        for part in file_path.parts:
            if any(part.startswith(exclude) for exclude in self.exclude_dirs):
                return False
        
        # 排除备份文件
        if 'backup' in str(file_path).lower():
            return False
        
        return True
    
    def update_file_content(self, file_path: Path) -> Tuple[bool, int]:
        """更新单个文件的内容"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                original_content = f.read()
            
            updated_content = original_content
            replacement_count = 0
            
            # 应用所有路径映射
            for old_pattern, new_path in self.path_mappings.items():
                matches = re.findall(old_pattern, updated_content)
                if matches:
                    updated_content = re.sub(old_pattern, new_path, updated_content)
                    replacement_count += len(matches)
            
            # 如果有更新，写回文件
            if replacement_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                
                logger.info(f"更新文件: {file_path} ({replacement_count} 处替换)")
                return True, replacement_count
            
            return False, 0
            
        except Exception as e:
            error_msg = f"处理文件失败 {file_path}: {e}"
            logger.error(error_msg)
            self.update_stats['errors'].append(error_msg)
            return False, 0
    
    def update_all_references(self, root_dir: str = ".") -> Dict:
        """更新所有路径引用"""
        logger.info("开始更新代码中的路径引用...")
        
        root_path = Path(root_dir)
        
        # 遍历所有文件
        for file_path in root_path.rglob("*"):
            if file_path.is_file() and self.should_process_file(file_path):
                self.update_stats['files_processed'] += 1
                
                updated, replacements = self.update_file_content(file_path)
                if updated:
                    self.update_stats['files_updated'] += 1
                    self.update_stats['total_replacements'] += replacements
        
        # 生成报告
        report = {
            'update_timestamp': '2025-07-23T09:30:00',
            'statistics': self.update_stats,
            'path_mappings_applied': len(self.path_mappings),
            'success_rate': (self.update_stats['files_processed'] - len(self.update_stats['errors'])) / max(self.update_stats['files_processed'], 1) * 100
        }
        
        # 保存报告
        report_path = Path("data/metadata/quality/path_update_report.json")
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"路径更新完成，报告保存到: {report_path}")
        return report
    
    def print_summary(self, report: Dict):
        """打印更新摘要"""
        print("\n" + "="*60)
        print("代码路径引用更新报告")
        print("="*60)
        
        stats = report['statistics']
        print(f"📊 处理统计:")
        print(f"   - 处理文件数: {stats['files_processed']}")
        print(f"   - 更新文件数: {stats['files_updated']}")
        print(f"   - 总替换次数: {stats['total_replacements']}")
        print(f"   - 成功率: {report['success_rate']:.1f}%")
        
        if stats['errors']:
            print(f"\n❌ 错误 ({len(stats['errors'])}):")
            for error in stats['errors'][:5]:  # 只显示前5个错误
                print(f"   - {error}")
            if len(stats['errors']) > 5:
                print(f"   - ... 还有 {len(stats['errors']) - 5} 个错误")
        
        if stats['files_updated'] > 0:
            print(f"\n✅ 成功更新 {stats['files_updated']} 个文件的路径引用")
        else:
            print(f"\n⚠️ 没有找到需要更新的路径引用")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="代码路径引用更新工具")
    parser.add_argument("--root-dir", default=".", help="根目录路径")
    parser.add_argument("--dry-run", action="store_true", help="只检查不更新")
    
    args = parser.parse_args()
    
    # 创建更新器
    updater = PathReferenceUpdater()
    
    if args.dry_run:
        logger.info("运行模式: 只检查不更新")
        # TODO: 实现干运行模式
        return
    
    # 执行更新
    report = updater.update_all_references(args.root_dir)
    
    # 打印摘要
    updater.print_summary(report)


if __name__ == "__main__":
    main()
