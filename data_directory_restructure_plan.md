# 数据目录结构重构方案

## 📊 当前问题分析

### 现状问题
当前`data/`目录下有**10个子目录**，存在严重的数据管理问题：

```
data/
├── annotations/          # 图片标注数据
├── cleaned/             # 清洗后数据（空目录）
├── equipment/           # 设备数据
├── generated/           # 生成的数据
├── integrated/          # 集成数据
├── massive_generated/   # 大量生成数据
├── processed/           # 处理后数据
├── professional_processed/ # 专业处理数据（空目录）
├── raw/                # 原始数据
├── structured/         # 结构化数据
├── test_cleaned/       # 测试清洗数据（空目录）
└── test_professional/  # 测试专业数据（空目录）
```

### 主要问题
1. **功能重复**：`generated/`、`massive_generated/`、`integrated/`功能重叠
2. **命名混乱**：`processed/`、`professional_processed/`、`test_professional/`含义不清
3. **数据分散**：同类型数据分布在不同目录
4. **空目录过多**：4个空目录占用空间
5. **缺乏层次**：没有按数据生命周期组织

## 🏗️ 重构方案设计

### 新的目录结构
```
data/
├── 01_raw/                    # 原始数据层
│   ├── equipment/            # 设备原始数据
│   ├── fault_cases/          # 故障案例原始数据
│   ├── sensor_data/          # 传感器数据
│   ├── maintenance_logs/     # 维护日志
│   ├── inspection_reports/   # 巡检报告
│   └── uploads/             # 用户上传文件
├── 02_processed/             # 处理数据层
│   ├── cleaned/             # 清洗后数据
│   ├── annotated/           # 标注数据
│   ├── structured/          # 结构化数据
│   └── validated/           # 验证后数据
├── 03_enhanced/              # 增强数据层
│   ├── generated/           # AI生成数据
│   ├── integrated/          # 集成数据
│   ├── knowledge_base/      # 知识库数据
│   └── training_sets/       # 训练数据集
├── 04_production/            # 生产数据层
│   ├── active/              # 当前使用数据
│   ├── cached/              # 缓存数据
│   ├── indexed/             # 索引数据
│   └── backups/             # 备份数据
├── 05_outputs/               # 输出数据层
│   ├── reports/             # 分析报告
│   ├── exports/             # 导出文件
│   ├── visualizations/      # 可视化结果
│   └── logs/                # 处理日志
└── metadata/                 # 元数据管理
    ├── schemas/             # 数据模式定义
    ├── catalogs/            # 数据目录
    ├── lineage/             # 数据血缘
    └── quality/             # 数据质量报告
```

### 设计原则
1. **数据生命周期管理**：按数据处理阶段编号（01-05）
2. **功能明确分离**：每个目录职责单一明确
3. **层次化组织**：从原始数据到生产数据的完整流程
4. **元数据管理**：独立的元数据管理体系
5. **可扩展性**：支持未来数据类型扩展

## 🔄 数据迁移计划

### 阶段1：创建新目录结构
```bash
# 创建新的目录结构
mkdir -p data/{01_raw,02_processed,03_enhanced,04_production,05_outputs,metadata}/{equipment,fault_cases,sensor_data,maintenance_logs,inspection_reports,uploads}
mkdir -p data/02_processed/{cleaned,annotated,structured,validated}
mkdir -p data/03_enhanced/{generated,integrated,knowledge_base,training_sets}
mkdir -p data/04_production/{active,cached,indexed,backups}
mkdir -p data/05_outputs/{reports,exports,visualizations,logs}
mkdir -p data/metadata/{schemas,catalogs,lineage,quality}
```

### 阶段2：数据迁移映射
```
# 原始数据迁移
raw/ → 01_raw/
├── equipment_photo_001.txt → 01_raw/equipment/
├── fault_cases/ → 01_raw/fault_cases/
├── sensor_data/ → 01_raw/sensor_data/
├── maintenance_log_001.json → 01_raw/maintenance_logs/
├── inspection_checklist_001.txt → 01_raw/inspection_reports/
└── uploads/ → 01_raw/uploads/

# 处理数据迁移
annotations/ → 02_processed/annotated/
structured/ → 02_processed/structured/
cleaned/ → 02_processed/cleaned/

# 增强数据迁移
generated/ → 03_enhanced/generated/
massive_generated/ → 03_enhanced/generated/advanced/
integrated/ → 03_enhanced/integrated/

# 生产数据迁移
equipment/ → 04_production/active/equipment/
processed/ → 04_production/cached/

# 输出数据迁移
processed/*.md → 05_outputs/reports/
processed/*.json → 05_outputs/logs/
```

### 阶段3：代码更新
需要更新以下文件中的路径引用：
- `ui/app.py`
- `data_processing/*.py`
- `retriever/*.py`
- `core/*.py`
- 配置文件

## 📋 数据管理策略

### 1. 数据分类标准
```python
class DataCategory:
    RAW = "01_raw"           # 原始数据，只读
    PROCESSED = "02_processed"  # 处理数据，可读写
    ENHANCED = "03_enhanced"    # 增强数据，AI生成
    PRODUCTION = "04_production" # 生产数据，高可用
    OUTPUTS = "05_outputs"      # 输出数据，可清理
```

### 2. 数据生命周期管理
```python
class DataLifecycle:
    def __init__(self):
        self.stages = {
            "ingestion": "01_raw",      # 数据摄入
            "processing": "02_processed", # 数据处理
            "enhancement": "03_enhanced", # 数据增强
            "production": "04_production", # 生产使用
            "archival": "05_outputs"     # 归档输出
        }
```

### 3. 数据质量管理
```python
class DataQualityManager:
    def validate_data_placement(self, file_path: str, data_type: str):
        """验证数据是否放置在正确目录"""
        expected_dir = self.get_expected_directory(data_type)
        return file_path.startswith(expected_dir)
    
    def generate_quality_report(self, directory: str):
        """生成数据质量报告"""
        return {
            "total_files": self.count_files(directory),
            "data_types": self.analyze_data_types(directory),
            "quality_score": self.calculate_quality_score(directory)
        }
```

### 4. 元数据管理
```python
class MetadataManager:
    def __init__(self):
        self.schema_registry = {}
        self.data_catalog = {}
        self.lineage_tracker = {}
    
    def register_schema(self, data_type: str, schema: dict):
        """注册数据模式"""
        self.schema_registry[data_type] = schema
    
    def track_lineage(self, source: str, target: str, operation: str):
        """跟踪数据血缘"""
        self.lineage_tracker[target] = {
            "source": source,
            "operation": operation,
            "timestamp": datetime.now()
        }
```

## 🚀 实施步骤

### 步骤1：备份现有数据
```bash
# 创建备份
cp -r data/ data_backup_$(date +%Y%m%d_%H%M%S)/
```

### 步骤2：创建新目录结构
```bash
# 执行目录创建脚本
python scripts/create_new_data_structure.py
```

### 步骤3：数据迁移
```bash
# 执行数据迁移脚本
python scripts/migrate_data.py
```

### 步骤4：更新代码引用
```bash
# 批量更新路径引用
python scripts/update_path_references.py
```

### 步骤5：验证和测试
```bash
# 运行验证脚本
python scripts/validate_data_structure.py
```

## 📈 预期收益

### 1. 管理效率提升
- **查找效率**：按生命周期组织，快速定位数据
- **维护成本**：统一管理策略，降低维护复杂度
- **扩展性**：标准化结构，支持新数据类型

### 2. 数据质量提升
- **一致性**：统一的数据组织标准
- **完整性**：完整的数据生命周期管理
- **可追溯性**：清晰的数据血缘关系

### 3. 开发效率提升
- **代码简化**：统一的路径管理
- **错误减少**：明确的数据分类
- **协作改善**：清晰的数据组织结构

这个重构方案将彻底解决当前数据目录混乱的问题，建立起专业的数据管理体系。
