#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级专业检索系统
重构检索引擎，提升检索准确度和相关性，缩小与标准大模型的差距
"""

import os
import re
import json
import math
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from loguru import logger
import jieba
import jieba.analyse

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.decomposition import LatentDirichletAllocation
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn不可用，将使用基础检索功能")


@dataclass
class RetrievalResult:
    """检索结果数据结构"""
    document_id: str
    title: str
    content: str
    relevance_score: float
    semantic_score: float
    technical_score: float
    quality_score: float
    metadata: Dict[str, Any]
    retrieval_method: str
    confidence_level: str
    key_matches: List[str]
    technical_parameters: Dict[str, Any]


@dataclass
class QueryAnalysis:
    """查询分析结果"""
    intent_type: str  # fault_diagnosis, equipment_analysis, operation_guidance
    equipment_types: List[str]
    fault_categories: List[str]
    technical_terms: List[str]
    voltage_levels: List[str]
    urgency_level: str  # low, medium, high, critical
    complexity_score: float
    semantic_keywords: List[str]


class AdvancedProfessionalRetriever:
    """高级专业检索系统"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # 专业知识图谱
        self.knowledge_graph = self._build_knowledge_graph()
        
        # 语义分析器
        self.semantic_analyzer = self._initialize_semantic_analyzer()
        
        # 技术术语词典
        self.technical_dictionary = self._build_technical_dictionary()
        
        # 检索策略配置
        self.retrieval_strategies = self._configure_retrieval_strategies()
        
        # 质量评估器
        self.quality_evaluator = self._initialize_quality_evaluator()
        
        logger.info("高级专业检索系统初始化完成")
    
    def _build_knowledge_graph(self) -> Dict[str, Any]:
        """构建专业知识图谱"""
        return {
            "equipment_ontology": {
                "变压器": {
                    "is_a": "电力设备",
                    "has_components": ["铁芯", "绕组", "套管", "冷却系统", "保护装置"],
                    "has_parameters": ["额定容量", "额定电压", "变比", "阻抗", "损耗"],
                    "common_faults": ["绕组故障", "铁芯故障", "套管渗油", "冷却故障"],
                    "related_protection": ["差动保护", "瓦斯保护", "温度保护"],
                    "voltage_levels": ["500kV", "220kV", "110kV", "35kV", "10kV"]
                },
                "断路器": {
                    "is_a": "开关设备",
                    "has_components": ["触头系统", "灭弧室", "操作机构", "支撑绝缘子"],
                    "has_parameters": ["额定电压", "额定电流", "开断电流", "操作时间"],
                    "common_faults": ["拒动", "误动", "触头烧损", "操作机构故障"],
                    "related_protection": ["失灵保护", "操作回路监视"],
                    "types": ["SF6断路器", "真空断路器", "油断路器"]
                }
            },
            "fault_taxonomy": {
                "电气故障": {
                    "subtypes": ["短路故障", "接地故障", "断线故障", "过载故障"],
                    "causes": ["绝缘击穿", "导体接触不良", "外力破坏", "设备老化"],
                    "symptoms": ["保护动作", "电流异常", "电压异常", "功率异常"],
                    "detection_methods": ["保护装置", "在线监测", "定期试验"]
                },
                "机械故障": {
                    "subtypes": ["操作故障", "结构故障", "密封故障", "冷却故障"],
                    "causes": ["磨损", "腐蚀", "疲劳", "松动", "变形"],
                    "symptoms": ["异音", "振动", "温升", "泄漏", "动作异常"],
                    "detection_methods": ["巡视检查", "状态监测", "定期检修"]
                }
            },
            "causal_relationships": {
                "绝缘老化": ["过电压", "过热", "污染", "湿度", "时间"],
                "触头烧损": ["过电流", "接触不良", "电弧", "材料质量"],
                "操作失灵": ["机构卡涩", "控制回路故障", "电源故障", "机械磨损"]
            },
            "semantic_associations": {
                "故障": ["异常", "缺陷", "损坏", "失效", "问题"],
                "检修": ["维护", "修理", "更换", "调整", "试验"],
                "监测": ["检测", "测量", "观察", "记录", "分析"]
            }
        }
    
    def _initialize_semantic_analyzer(self) -> Optional[Any]:
        """初始化语义分析器"""
        if not SKLEARN_AVAILABLE:
            return None
        
        try:
            # 配置TF-IDF向量化器
            vectorizer = TfidfVectorizer(
                max_features=5000,
                ngram_range=(1, 3),
                stop_words=None,  # 保留专业术语
                min_df=2,
                max_df=0.8,
                sublinear_tf=True
            )
            
            return {
                'tfidf_vectorizer': vectorizer,
                'similarity_threshold': 0.3,
                'semantic_weight': 0.4
            }
            
        except Exception as e:
            logger.error(f"语义分析器初始化失败: {str(e)}")
            return None
    
    def _build_technical_dictionary(self) -> Dict[str, Any]:
        """构建技术术语词典"""
        return {
            "equipment_terms": {
                "primary": ["变压器", "断路器", "隔离开关", "互感器", "避雷器", "电抗器"],
                "secondary": ["保护装置", "测控装置", "通信设备", "直流系统", "监控系统"],
                "components": ["套管", "绕组", "铁芯", "触头", "操作机构", "绝缘子"],
                "weights": {"primary": 1.0, "secondary": 0.8, "components": 0.6}
            },
            "fault_terms": {
                "electrical": ["短路", "接地", "断线", "过载", "过压", "欠压", "谐波"],
                "mechanical": ["卡涩", "松动", "磨损", "变形", "断裂", "腐蚀", "振动"],
                "thermal": ["过热", "温升", "冷却不良", "热老化", "热膨胀"],
                "insulation": ["绝缘击穿", "绝缘老化", "局部放电", "受潮", "污闪"],
                "weights": {"electrical": 1.0, "mechanical": 0.9, "thermal": 0.8, "insulation": 0.9}
            },
            "technical_parameters": {
                "electrical": ["电压", "电流", "功率", "频率", "阻抗", "电阻"],
                "mechanical": ["压力", "温度", "振动", "位移", "力矩", "速度"],
                "protection": ["动作时间", "返回时间", "灵敏度", "选择性", "可靠性"],
                "weights": {"electrical": 1.0, "mechanical": 0.8, "protection": 0.9}
            },
            "analysis_methods": {
                "diagnostic": ["故障录波", "保护动作", "状态分析", "参数分析"],
                "testing": ["绝缘试验", "机械特性", "保护校验", "设备巡检"],
                "monitoring": ["在线监测", "状态评估", "趋势分析", "预警分析"],
                "weights": {"diagnostic": 1.0, "testing": 0.9, "monitoring": 0.8}
            }
        }
    
    def _configure_retrieval_strategies(self) -> Dict[str, Any]:
        """配置检索策略"""
        return {
            "multi_stage_retrieval": {
                "stage1": {"method": "keyword_matching", "weight": 0.3, "top_k": 50},
                "stage2": {"method": "semantic_similarity", "weight": 0.4, "top_k": 30},
                "stage3": {"method": "technical_relevance", "weight": 0.3, "top_k": 20}
            },
            "scoring_weights": {
                "keyword_match": 0.25,
                "semantic_similarity": 0.35,
                "technical_relevance": 0.25,
                "quality_score": 0.15
            },
            "filtering_criteria": {
                "min_relevance_score": 0.4,
                "max_results": 15,
                "diversity_threshold": 0.7,
                "quality_threshold": 0.6
            },
            "ranking_factors": {
                "relevance": 0.4,
                "quality": 0.3,
                "recency": 0.1,
                "authority": 0.1,
                "completeness": 0.1
            }
        }
    
    def _initialize_quality_evaluator(self) -> Dict[str, Any]:
        """初始化质量评估器"""
        return {
            "content_quality_metrics": {
                "length_score": {"min_chars": 200, "optimal_chars": 1000, "weight": 0.2},
                "technical_density": {"min_ratio": 0.1, "optimal_ratio": 0.2, "weight": 0.3},
                "structure_score": {"required_sections": 3, "weight": 0.2},
                "parameter_richness": {"min_params": 2, "weight": 0.3}
            },
            "relevance_metrics": {
                "keyword_coverage": {"weight": 0.4},
                "semantic_alignment": {"weight": 0.3},
                "context_coherence": {"weight": 0.3}
            },
            "authority_metrics": {
                "source_credibility": {"weight": 0.5},
                "technical_accuracy": {"weight": 0.3},
                "practical_value": {"weight": 0.2}
            }
        }
    
    def advanced_retrieve(self, query: str, documents: List[Dict[str, Any]], top_k: int = 10) -> List[RetrievalResult]:
        """高级专业检索"""
        try:
            logger.info(f"开始高级专业检索: {query[:50]}...")
            
            # 1. 查询分析
            query_analysis = self._analyze_query_comprehensively(query)
            logger.info(f"查询意图: {query_analysis.intent_type}, 复杂度: {query_analysis.complexity_score:.2f}")
            
            # 2. 多阶段检索
            stage1_results = self._keyword_based_retrieval(query, documents, query_analysis)
            stage2_results = self._semantic_based_retrieval(query, stage1_results, query_analysis)
            stage3_results = self._technical_relevance_retrieval(query, stage2_results, query_analysis)
            
            # 3. 综合评分和排序
            scored_results = self._comprehensive_scoring(query, stage3_results, query_analysis)
            
            # 4. 结果过滤和多样性优化
            filtered_results = self._filter_and_diversify(scored_results, top_k)
            
            # 5. 构建最终结果
            final_results = self._build_retrieval_results(filtered_results, query_analysis)
            
            logger.info(f"高级检索完成，返回 {len(final_results)} 个高质量结果")
            return final_results[:top_k]
            
        except Exception as e:
            logger.error(f"高级专业检索失败: {str(e)}")
            return self._fallback_retrieval(query, documents, top_k)
    
    def _analyze_query_comprehensively(self, query: str) -> QueryAnalysis:
        """全面分析查询"""
        try:
            query_lower = query.lower()
            
            # 意图识别
            intent_type = self._identify_query_intent(query_lower)
            
            # 设备类型识别
            equipment_types = self._extract_equipment_types(query_lower)
            
            # 故障类别识别
            fault_categories = self._extract_fault_categories(query_lower)
            
            # 技术术语提取
            technical_terms = self._extract_technical_terms(query)
            
            # 电压等级提取
            voltage_levels = self._extract_voltage_levels(query_lower)
            
            # 紧急程度评估
            urgency_level = self._assess_urgency_level(query_lower)
            
            # 复杂度评分
            complexity_score = self._calculate_query_complexity(query, technical_terms)
            
            # 语义关键词扩展
            semantic_keywords = self._expand_semantic_keywords(query, technical_terms)
            
            return QueryAnalysis(
                intent_type=intent_type,
                equipment_types=equipment_types,
                fault_categories=fault_categories,
                technical_terms=technical_terms,
                voltage_levels=voltage_levels,
                urgency_level=urgency_level,
                complexity_score=complexity_score,
                semantic_keywords=semantic_keywords
            )
            
        except Exception as e:
            logger.error(f"查询分析失败: {str(e)}")
            return self._create_default_query_analysis(query)
    
    def _identify_query_intent(self, query_lower: str) -> str:
        """识别查询意图"""
        intent_patterns = {
            "fault_diagnosis": ["故障", "异常", "问题", "缺陷", "损坏", "失效"],
            "equipment_analysis": ["设备", "装置", "器件", "系统", "参数", "性能"],
            "operation_guidance": ["操作", "运行", "维护", "检修", "调整", "控制"],
            "technical_consultation": ["原理", "机理", "分析", "计算", "设计", "选型"]
        }
        
        intent_scores = {}
        for intent, keywords in intent_patterns.items():
            score = sum(1 for keyword in keywords if keyword in query_lower)
            intent_scores[intent] = score
        
        return max(intent_scores, key=intent_scores.get) if intent_scores else "general_inquiry"
    
    def _extract_equipment_types(self, query_lower: str) -> List[str]:
        """提取设备类型"""
        equipment_types = []
        
        for category, terms in self.technical_dictionary["equipment_terms"].items():
            if category != "weights":
                for term in terms:
                    if term.lower() in query_lower:
                        equipment_types.append(term)
        
        return list(set(equipment_types))
    
    def _extract_fault_categories(self, query_lower: str) -> List[str]:
        """提取故障类别"""
        fault_categories = []
        
        for category, terms in self.technical_dictionary["fault_terms"].items():
            if category != "weights":
                for term in terms:
                    if term.lower() in query_lower:
                        fault_categories.append(term)
        
        return list(set(fault_categories))
    
    def _extract_technical_terms(self, query: str) -> List[str]:
        """提取技术术语"""
        try:
            # 使用jieba进行关键词提取
            keywords = jieba.analyse.extract_tags(query, topK=10, withWeight=False)
            
            # 过滤专业术语
            technical_terms = []
            for keyword in keywords:
                if self._is_technical_term(keyword):
                    technical_terms.append(keyword)
            
            return technical_terms
            
        except Exception as e:
            logger.error(f"技术术语提取失败: {str(e)}")
            return []
