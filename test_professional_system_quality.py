#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的专业系统质量
验证数据处理、提示词工程、检索系统的改进效果
"""

import os
import sys
import json
import time
import requests
from typing import Dict, List, Any
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_processing.professional_data_processor import ProfessionalDataProcessor, TechnicalDocument
from langchain_modules.prompts.professional_prompt_engine import ProfessionalPromptEngine, ContextualInformation
from retriever.advanced_professional_retriever import AdvancedProfessionalRetriever, RetrievalResult


class ProfessionalSystemQualityTester:
    """专业系统质量测试器"""
    
    def __init__(self):
        self.test_results = {
            "data_processing": {},
            "prompt_engineering": {},
            "retrieval_system": {},
            "end_to_end": {},
            "overall_assessment": {}
        }
        
        # 测试用例
        self.test_cases = [
            {
                "query": "110kV变压器套管渗油故障的深度分析和处理方案",
                "expected_quality": {
                    "technical_depth": 0.85,
                    "professional_accuracy": 0.90,
                    "practical_value": 0.85,
                    "structural_clarity": 0.90
                }
            },
            {
                "query": "SF6断路器操作机构拒动的故障机理分析",
                "expected_quality": {
                    "technical_depth": 0.80,
                    "professional_accuracy": 0.85,
                    "practical_value": 0.80,
                    "structural_clarity": 0.85
                }
            },
            {
                "query": "220kV线路差动保护误动的原因分析和改进措施",
                "expected_quality": {
                    "technical_depth": 0.90,
                    "professional_accuracy": 0.95,
                    "practical_value": 0.85,
                    "structural_clarity": 0.90
                }
            }
        ]
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合质量测试"""
        print("🧪 开始专业系统质量综合测试...")
        print("="*80)
        
        # 1. 测试数据处理质量
        print("\n📊 测试数据处理质量...")
        data_processing_results = self._test_data_processing_quality()
        self.test_results["data_processing"] = data_processing_results
        
        # 2. 测试提示词工程质量
        print("\n🎯 测试提示词工程质量...")
        prompt_engineering_results = self._test_prompt_engineering_quality()
        self.test_results["prompt_engineering"] = prompt_engineering_results
        
        # 3. 测试检索系统质量
        print("\n🔍 测试检索系统质量...")
        retrieval_results = self._test_retrieval_system_quality()
        self.test_results["retrieval_system"] = retrieval_results
        
        # 4. 测试端到端质量
        print("\n🚀 测试端到端系统质量...")
        end_to_end_results = self._test_end_to_end_quality()
        self.test_results["end_to_end"] = end_to_end_results
        
        # 5. 综合评估
        print("\n📈 进行综合质量评估...")
        overall_assessment = self._conduct_overall_assessment()
        self.test_results["overall_assessment"] = overall_assessment
        
        # 保存测试结果
        self._save_test_results()
        
        # 打印测试报告
        self._print_test_report()
        
        return self.test_results
    
    def _test_data_processing_quality(self) -> Dict[str, Any]:
        """测试数据处理质量"""
        try:
            # 初始化专业数据处理器
            config = {"output_dir": "data/test_professional_processed"}
            processor = ProfessionalDataProcessor(config)
            
            # 准备测试数据
            test_documents = self._prepare_test_documents()
            
            # 执行专业处理
            processed_docs, metrics = processor.process_document_batch(test_documents)
            
            # 评估处理质量
            quality_assessment = {
                "processing_success_rate": metrics.processed_documents / metrics.total_documents,
                "quality_pass_rate": metrics.quality_passed / max(metrics.processed_documents, 1),
                "technical_depth_score": metrics.technical_depth_score,
                "professional_terminology_score": metrics.professional_terminology_score,
                "structural_completeness_score": metrics.structural_completeness_score,
                "overall_quality_score": metrics.overall_quality_score,
                "processed_document_samples": [
                    {
                        "id": doc.id,
                        "title": doc.title,
                        "quality_score": doc.quality_score,
                        "analysis_depth": doc.analysis_depth,
                        "technical_parameters_count": len(doc.technical_parameters)
                    }
                    for doc in processed_docs[:3]
                ]
            }
            
            print(f"   ✅ 数据处理成功率: {quality_assessment['processing_success_rate']*100:.1f}%")
            print(f"   ✅ 质量通过率: {quality_assessment['quality_pass_rate']*100:.1f}%")
            print(f"   ✅ 整体质量评分: {quality_assessment['overall_quality_score']:.3f}")
            
            return quality_assessment
            
        except Exception as e:
            print(f"   ❌ 数据处理测试失败: {str(e)}")
            return {"error": str(e), "success": False}
    
    def _test_prompt_engineering_quality(self) -> Dict[str, Any]:
        """测试提示词工程质量"""
        try:
            # 初始化专业提示词引擎
            config = {}
            prompt_engine = ProfessionalPromptEngine(config)
            
            prompt_quality_results = []
            
            for test_case in self.test_cases:
                query = test_case["query"]
                
                # 构建模拟上下文
                mock_context = self._create_mock_context_info()
                
                # 生成专业提示词
                r1_prompt = prompt_engine.generate_professional_prompt(
                    "deepseek_r1_professional", query, mock_context
                )
                v3_prompt = prompt_engine.generate_professional_prompt(
                    "deepseek_v3_professional", query, mock_context
                )
                
                # 评估提示词质量
                r1_quality = self._evaluate_prompt_quality(r1_prompt, "deepseek_r1")
                v3_quality = self._evaluate_prompt_quality(v3_prompt, "deepseek_v3")
                
                prompt_quality_results.append({
                    "query": query,
                    "r1_prompt_quality": r1_quality,
                    "v3_prompt_quality": v3_quality,
                    "r1_prompt_length": len(r1_prompt),
                    "v3_prompt_length": len(v3_prompt)
                })
            
            # 计算平均质量
            avg_r1_quality = sum(r["r1_prompt_quality"]["overall_score"] for r in prompt_quality_results) / len(prompt_quality_results)
            avg_v3_quality = sum(r["v3_prompt_quality"]["overall_score"] for r in prompt_quality_results) / len(prompt_quality_results)
            
            quality_assessment = {
                "r1_average_quality": avg_r1_quality,
                "v3_average_quality": avg_v3_quality,
                "overall_prompt_quality": (avg_r1_quality + avg_v3_quality) / 2,
                "detailed_results": prompt_quality_results
            }
            
            print(f"   ✅ R1提示词平均质量: {avg_r1_quality:.3f}")
            print(f"   ✅ V3提示词平均质量: {avg_v3_quality:.3f}")
            print(f"   ✅ 整体提示词质量: {quality_assessment['overall_prompt_quality']:.3f}")
            
            return quality_assessment
            
        except Exception as e:
            print(f"   ❌ 提示词工程测试失败: {str(e)}")
            return {"error": str(e), "success": False}
    
    def _test_retrieval_system_quality(self) -> Dict[str, Any]:
        """测试检索系统质量"""
        try:
            # 初始化高级检索器
            config = {}
            retriever = AdvancedProfessionalRetriever(config)
            
            # 准备测试文档
            test_documents = self._prepare_test_documents()
            
            retrieval_quality_results = []
            
            for test_case in self.test_cases:
                query = test_case["query"]
                
                # 执行高级检索
                retrieval_results = retriever.advanced_retrieve(query, test_documents, top_k=10)
                
                # 评估检索质量
                retrieval_quality = self._evaluate_retrieval_quality(query, retrieval_results)
                
                retrieval_quality_results.append({
                    "query": query,
                    "results_count": len(retrieval_results),
                    "average_relevance": sum(r.relevance_score for r in retrieval_results) / max(len(retrieval_results), 1),
                    "average_quality": sum(r.quality_score for r in retrieval_results) / max(len(retrieval_results), 1),
                    "retrieval_quality_assessment": retrieval_quality
                })
            
            # 计算整体检索质量
            avg_relevance = sum(r["average_relevance"] for r in retrieval_quality_results) / len(retrieval_quality_results)
            avg_quality = sum(r["average_quality"] for r in retrieval_quality_results) / len(retrieval_quality_results)
            
            quality_assessment = {
                "average_relevance_score": avg_relevance,
                "average_quality_score": avg_quality,
                "overall_retrieval_quality": (avg_relevance + avg_quality) / 2,
                "detailed_results": retrieval_quality_results
            }
            
            print(f"   ✅ 平均相关性评分: {avg_relevance:.3f}")
            print(f"   ✅ 平均质量评分: {avg_quality:.3f}")
            print(f"   ✅ 整体检索质量: {quality_assessment['overall_retrieval_quality']:.3f}")
            
            return quality_assessment
            
        except Exception as e:
            print(f"   ❌ 检索系统测试失败: {str(e)}")
            return {"error": str(e), "success": False}
    
    def _test_end_to_end_quality(self) -> Dict[str, Any]:
        """测试端到端系统质量"""
        try:
            print("   🔄 测试与实际API的集成...")
            
            base_url = "http://localhost:5002"
            end_to_end_results = []
            
            for test_case in self.test_cases:
                query = test_case["query"]
                expected_quality = test_case["expected_quality"]
                
                # 测试DeepSeek-R1
                r1_result = self._test_api_call(base_url, query, "deepseek-r1")
                r1_quality = self._evaluate_api_response_quality(r1_result, expected_quality)
                
                # 测试DeepSeek-V3
                v3_result = self._test_api_call(base_url, query, "deepseek-v3")
                v3_quality = self._evaluate_api_response_quality(v3_result, expected_quality)
                
                end_to_end_results.append({
                    "query": query,
                    "r1_api_result": r1_result,
                    "v3_api_result": v3_result,
                    "r1_quality_assessment": r1_quality,
                    "v3_quality_assessment": v3_quality
                })
            
            # 计算整体端到端质量
            successful_tests = [r for r in end_to_end_results if r["r1_api_result"].get("success") or r["v3_api_result"].get("success")]
            success_rate = len(successful_tests) / len(end_to_end_results)
            
            if successful_tests:
                avg_r1_quality = sum(r["r1_quality_assessment"]["overall_score"] for r in successful_tests if r["r1_api_result"].get("success")) / max(sum(1 for r in successful_tests if r["r1_api_result"].get("success")), 1)
                avg_v3_quality = sum(r["v3_quality_assessment"]["overall_score"] for r in successful_tests if r["v3_api_result"].get("success")) / max(sum(1 for r in successful_tests if r["v3_api_result"].get("success")), 1)
            else:
                avg_r1_quality = 0.0
                avg_v3_quality = 0.0
            
            quality_assessment = {
                "success_rate": success_rate,
                "r1_average_quality": avg_r1_quality,
                "v3_average_quality": avg_v3_quality,
                "overall_end_to_end_quality": (avg_r1_quality + avg_v3_quality) / 2,
                "detailed_results": end_to_end_results
            }
            
            print(f"   ✅ 端到端成功率: {success_rate*100:.1f}%")
            print(f"   ✅ R1端到端质量: {avg_r1_quality:.3f}")
            print(f"   ✅ V3端到端质量: {avg_v3_quality:.3f}")
            
            return quality_assessment
            
        except Exception as e:
            print(f"   ❌ 端到端测试失败: {str(e)}")
            return {"error": str(e), "success": False}
    
    def _conduct_overall_assessment(self) -> Dict[str, Any]:
        """进行综合质量评估"""
        try:
            # 提取各模块质量分数
            data_processing_score = self.test_results["data_processing"].get("overall_quality_score", 0.0)
            prompt_engineering_score = self.test_results["prompt_engineering"].get("overall_prompt_quality", 0.0)
            retrieval_score = self.test_results["retrieval_system"].get("overall_retrieval_quality", 0.0)
            end_to_end_score = self.test_results["end_to_end"].get("overall_end_to_end_quality", 0.0)
            
            # 计算加权综合分数
            weights = {
                "data_processing": 0.25,
                "prompt_engineering": 0.25,
                "retrieval_system": 0.25,
                "end_to_end": 0.25
            }
            
            overall_score = (
                data_processing_score * weights["data_processing"] +
                prompt_engineering_score * weights["prompt_engineering"] +
                retrieval_score * weights["retrieval_system"] +
                end_to_end_score * weights["end_to_end"]
            )
            
            # 质量等级评定
            if overall_score >= 0.9:
                quality_grade = "优秀 (Excellent)"
            elif overall_score >= 0.8:
                quality_grade = "良好 (Good)"
            elif overall_score >= 0.7:
                quality_grade = "合格 (Acceptable)"
            elif overall_score >= 0.6:
                quality_grade = "需要改进 (Needs Improvement)"
            else:
                quality_grade = "不合格 (Poor)"
            
            # 与标准大模型的差距评估
            standard_model_benchmark = 0.85  # 假设标准大模型的基准分数
            gap_percentage = ((standard_model_benchmark - overall_score) / standard_model_benchmark) * 100
            
            assessment = {
                "overall_quality_score": overall_score,
                "quality_grade": quality_grade,
                "module_scores": {
                    "data_processing": data_processing_score,
                    "prompt_engineering": prompt_engineering_score,
                    "retrieval_system": retrieval_score,
                    "end_to_end": end_to_end_score
                },
                "gap_with_standard_model": {
                    "gap_percentage": max(gap_percentage, 0),
                    "improvement_needed": gap_percentage > 0,
                    "benchmark_score": standard_model_benchmark
                },
                "recommendations": self._generate_improvement_recommendations(overall_score, {
                    "data_processing": data_processing_score,
                    "prompt_engineering": prompt_engineering_score,
                    "retrieval_system": retrieval_score,
                    "end_to_end": end_to_end_score
                })
            }
            
            return assessment
            
        except Exception as e:
            print(f"   ❌ 综合评估失败: {str(e)}")
            return {"error": str(e), "success": False}
