// 故障分析智能助手 - 主JavaScript文件

// 全局变量
let currentTab = 'fault-analysis';
let analysisHistory = [];
let equipmentList = [];

// API基础URL
const API_BASE_URL = '/api/v1';

// Bootstrap替代方案 - 简单的模态框和Tab实现
// 检查是否已经存在bootstrap对象，避免重复定义
if (typeof window.bootstrap === 'undefined') {
    window.bootstrap = {};
}

// 安全的Modal类定义
if (typeof window.bootstrap.Modal === 'undefined') {
    window.bootstrap.Modal = class BootstrapModal {
        constructor(element) {
            this.element = typeof element === 'string' ? document.getElementById(element) : element;
        }

        show() {
            if (this.element) {
                this.element.style.display = 'block';
                this.element.classList.add('show');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        hide() {
            if (this.element) {
                this.element.style.display = 'none';
                this.element.classList.remove('show');
                document.body.style.overflow = ''; // 恢复滚动
            }
        }

        static getInstance(element) {
            // 简单实现：返回一个新实例
            return new window.bootstrap.Modal(element);
        }
    };
}

// 安全的Tab类定义
if (typeof window.bootstrap.Tab === 'undefined') {
    window.bootstrap.Tab = class BootstrapTab {
        constructor(element) {
            this.element = typeof element === 'string' ? document.querySelector(element) : element;
        }

        show() {
            if (!this.element) return;

            // 获取目标面板
            const target = this.element.getAttribute('href') || this.element.getAttribute('data-bs-target');
            if (!target) return;

            // 隐藏所有tab内容
            const tabContent = document.querySelectorAll('.tab-content');
            tabContent.forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });

            // 移除所有tab的active状态
            const allTabs = document.querySelectorAll('.nav-link');
            allTabs.forEach(tab => tab.classList.remove('active'));

            // 激活当前tab
            this.element.classList.add('active');

            // 显示目标内容
            const targetContent = document.querySelector(target);
            if (targetContent) {
                targetContent.classList.add('active');
                targetContent.style.display = 'block';
            }
        }
    };
}

// 安全的Alert类定义
if (typeof window.bootstrap.Alert === 'undefined') {
    window.bootstrap.Alert = class BootstrapAlert {
        constructor(element) {
            this.element = typeof element === 'string' ? document.getElementById(element) : element;
        }

        close() {
            if (this.element) {
                this.element.style.opacity = '0';
                setTimeout(() => {
                    if (this.element && this.element.parentNode) {
                        this.element.parentNode.removeChild(this.element);
                    }
                }, 300);
            }
        }

        static getInstance(element) {
            return new window.bootstrap.Alert(element);
        }
    };
}

// 安全的Toast类定义
if (typeof window.bootstrap.Toast === 'undefined') {
    window.bootstrap.Toast = class BootstrapToast {
        constructor(element, options = {}) {
            this.element = typeof element === 'string' ? document.getElementById(element) : element;
            this.options = {
                autohide: options.autohide !== false,
                delay: options.delay || 5000
            };
        }

        show() {
            if (this.element) {
                this.element.style.display = 'block';
                this.element.classList.add('show');

                if (this.options.autohide) {
                    setTimeout(() => {
                        this.hide();
                    }, this.options.delay);
                }
            }
        }

        hide() {
            if (this.element) {
                this.element.style.opacity = '0';
                setTimeout(() => {
                    this.element.style.display = 'none';
                    this.element.classList.remove('show');
                    if (this.element.parentNode) {
                        this.element.parentNode.removeChild(this.element);
                    }
                }, 300);
            }
        }

        static getInstance(element) {
            return new window.bootstrap.Toast(element);
        }
    };
}

// 格式化纯文本内容，处理加粗标记
// 简化的文本格式化函数
function formatPureTextContent(content) {
    if (!content) return content;
    return content.replace(/\n/g, '<br>');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();

});

// 清理页面残留元素
function cleanupPageElements() {
    // 移除可能的残留模态框
    const modals = document.querySelectorAll('.modal:not([id])');
    modals.forEach(modal => modal.remove());

    // 移除可能的残留通知
    const notifications = document.querySelectorAll('[style*="position: fixed"]:not(#notification-container)');
    notifications.forEach(notification => notification.remove());

    // 移除可能的残留按钮或元素
    const floatingElements = document.querySelectorAll('[style*="float: right"], [style*="position: absolute"]');
    floatingElements.forEach(element => {
        if (!element.closest('.card') && !element.closest('.modal') && !element.closest('.btn-group')) {
            element.remove();
        }
    });

    // 移除可能的残留标注元素
    const annotationElements = document.querySelectorAll('[class*="annotation"], [id*="annotation"]');
    annotationElements.forEach(element => {
        if (!element.closest('.modal')) {
            element.remove();
        }
    });

}

// 初始化应用
function initializeApp() {

    // 清理可能的残留元素
    cleanupPageElements();

    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化表单事件
    initFormEvents();

    // 初始化故障分析表单事件
    initFaultAnalysisFormEvents();

    // 初始化Bootstrap标签页
    initBootstrapTabs();

    // 初始化AI搜索功能
    initAISearch();

    // 初始化批量上传功能
    initBatchUpload();

    // 初始化图片拖拽上传功能
    initImageDragDrop();

    // 初始化设备管理功能
    initEquipmentManagement();

    // 初始化知识库增强功能
    initializeKnowledgeEnhancements();

    // 初始化模态框事件
    initModalEvents();

    // 检查系统状态
    checkSystemStatus();

    // 延迟测试知识库功能
    setTimeout(() => {
        testKnowledgeBaseFunctions();
    }, 1000);

    // 加载初始数据
    loadInitialData();

}

// 初始化标签页切换
function initTabSwitching() {
    const navLinks = document.querySelectorAll('.nav-link[data-tab]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetTab = this.getAttribute('data-tab');
            switchTab(targetTab);
        });
    });
}

// 安全的DOM操作辅助函数
function safeGetElement(selector, isId = false) {
    try {
        const element = isId ? document.getElementById(selector) : document.querySelector(selector);
        return element;
    } catch (error) {

        return null;
    }
}

function safeGetElements(selector) {
    try {
        return document.querySelectorAll(selector);
    } catch (error) {

        return [];
    }
}

function safeClassListOperation(element, operation, className) {
    if (element && element.classList) {
        try {
            element.classList[operation](className);
        } catch (error) {

        }
    }
}

// 显示模态框
function showModal(modalId) {

    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        modal.classList.add('show');
        // 添加背景遮罩
        document.body.classList.add('modal-open');

        // 创建背景遮罩
        let backdrop = document.querySelector('.modal-backdrop');
        if (!backdrop) {
            backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
        }
    } else {
        console.error('找不到模态框:', modalId);
    }
}

// 关闭模态框
function closeModal(modalId) {

    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        // 移除背景遮罩
        document.body.classList.remove('modal-open');

        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }
    }
}

// 初始化模态框关闭事件
function initModalEvents() {
    // 为所有关闭按钮添加事件监听
    document.addEventListener('click', function(e) {
        // 关闭按钮
        if (e.target.matches('[data-bs-dismiss="modal"], .btn-close, .modal-close')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                closeModal(modal.id);
            }
        }

        // 点击背景关闭
        if (e.target.classList.contains('modal-backdrop')) {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });
}

// 初始化模态框关闭事件
function initModalEvents() {
    // 为所有关闭按钮添加事件监听
    document.addEventListener('click', function(e) {
        // 关闭按钮
        if (e.target.matches('[data-bs-dismiss="modal"], .btn-close, .modal-close')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                closeModal(modal.id);
            }
        }

        // 点击背景关闭
        if (e.target.classList.contains('modal-backdrop')) {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });
}

// 切换标签页
function switchTab(tabName) {
    // 更新导航栏状态
    const navLinks = safeGetElements('.nav-link');
    navLinks.forEach(link => {
        safeClassListOperation(link, 'remove', 'active');
    });

    // 安全地查找并激活导航链接
    const navLink = safeGetElement(`[data-tab="${tabName}"]`);
    if (navLink) {
        safeClassListOperation(navLink, 'add', 'active');
    }

    // 更新内容区域
    const contentAreas = safeGetElements('.main-tab-content');
    contentAreas.forEach(content => {
        safeClassListOperation(content, 'remove', 'active');
    });

    // 安全地查找并激活内容区域
    const contentArea = safeGetElement(tabName, true);
    if (contentArea) {
        safeClassListOperation(contentArea, 'add', 'active');
    }

    currentTab = tabName;

    // 根据标签页加载相应数据 - 优化加载逻辑
    switch(tabName) {
        case 'equipment-management':
            // 只在设备列表为空或数据过期时才重新加载
            if (!window.equipmentList || window.equipmentList.length === 0 || isEquipmentDataStale()) {

                loadEquipmentList();
            } else {

                updateEquipmentTableDisplay();
            }
            break;
        case 'knowledge-base':
            // 知识库相关初始化
            break;
        case 'file-upload':
            // 文件上传相关初始化
            break;
    }
}

// 初始化表单事件
function initFormEvents() {
    // 故障分析表单
    const faultAnalysisForm = document.getElementById('fault-analysis-form');
    if (faultAnalysisForm) {
        faultAnalysisForm.addEventListener('submit', handleFaultAnalysis);
    }

    // 设备管理表单
    const equipmentForm = document.getElementById('equipment-form');
    if (equipmentForm) {
        equipmentForm.addEventListener('submit', handleAddEquipment);
    }

    // 设备搜索
    const equipmentSearch = document.getElementById('equipment-search');
    if (equipmentSearch) {
        equipmentSearch.addEventListener('input', handleEquipmentSearch);
    }
}

// 检查系统状态
async function checkSystemStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/status`);
        const status = await response.json();
        
        updateSystemStatusDisplay(status);
    } catch (error) {
        console.error('检查系统状态失败:', error);
        updateSystemStatusDisplay({ status: 'error', message: '系统连接失败' });
    }
}

// 更新系统状态显示
function updateSystemStatusDisplay(status) {
    const statusElement = document.getElementById('system-status');

    // 检查元素是否存在
    if (!statusElement) {

        return;
    }

    if (status.status === 'healthy') {
        statusElement.innerHTML = '<i class="bi bi-circle-fill text-success"></i> 系统正常';
    } else {
        statusElement.innerHTML = '<i class="bi bi-circle-fill text-danger"></i> 系统异常';
    }
}

// 加载初始数据
function loadInitialData() {

    loadAnalysisHistory();
    // 初始化时不自动加载设备列表，等用户切换到设备管理页面时再加载
    loadEquipmentOptionsForFaultAnalysis();

}

// 检查设备数据是否过期（5分钟）
function isEquipmentDataStale() {
    if (!window.equipmentDataTimestamp) {
        return true;
    }
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;
    return (now - window.equipmentDataTimestamp) > fiveMinutes;
}

// 为故障分析页面加载设备选项
async function loadEquipmentOptionsForFaultAnalysis() {
    try {
        const response = await fetch(`${API_BASE_URL}/equipment`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        if (data && data.equipment && Array.isArray(data.equipment)) {
            // 更新设备编号输入框为下拉选择
            const equipmentNumberInput = document.getElementById('equipment-number');
            if (equipmentNumberInput) {
                // 创建datalist元素用于自动完成
                let datalist = document.getElementById('equipment-list');
                if (!datalist) {
                    datalist = document.createElement('datalist');
                    datalist.id = 'equipment-list';
                    equipmentNumberInput.parentNode.appendChild(datalist);
                    equipmentNumberInput.setAttribute('list', 'equipment-list');
                }

                // 清空现有选项
                datalist.innerHTML = '';

                // 添加设备选项
                data.equipment.forEach(equipment => {
                    const option = document.createElement('option');
                    option.value = equipment.id;
                    option.textContent = `${equipment.name} (${equipment.id})`;
                    datalist.appendChild(option);
                });

            }
        }
    } catch (error) {
        console.error('加载设备选项失败:', error);
    }
}

// 初始化故障分析表单事件
function initFaultAnalysisFormEvents() {
    // 设备编号选择事件
    const equipmentNumberInput = document.getElementById('equipment-number');
    if (equipmentNumberInput) {
        equipmentNumberInput.addEventListener('change', function() {
            const selectedEquipmentId = this.value;
            if (selectedEquipmentId && equipmentList.length > 0) {
                const equipment = equipmentList.find(eq => eq.id === selectedEquipmentId);
                if (equipment) {
                    autoFillEquipmentInfo(equipment);
                }
            }
        });
    }
}

// 自动填充设备信息
function autoFillEquipmentInfo(equipment) {
    // 填充设备类型
    const equipmentTypeSelect = document.getElementById('equipment-type');
    if (equipmentTypeSelect && equipment.type) {
        // 映射设备类型
        const typeMapping = {
            'power_transformer': 'transformer',
            'circuit_breaker': 'breaker',
            'disconnector': 'switch'
        };
        const mappedType = typeMapping[equipment.type] || equipment.type;
        equipmentTypeSelect.value = mappedType;
    }

    // 填充制造商信息（如果有的话）
    const manufacturerInput = document.getElementById('manufacturer');
    if (manufacturerInput && equipment.manufacturer) {
        manufacturerInput.value = equipment.manufacturer;
    }

}

// 初始化Bootstrap标签页
function initBootstrapTabs() {
    // 让Bootstrap处理标签页切换，我们只监听切换事件
    const faultAnalysisTabs = document.querySelectorAll('#fault-analysis-tabs .nav-link');
    faultAnalysisTabs.forEach(tab => {
        // 监听Bootstrap的tab显示事件
        tab.addEventListener('shown.bs.tab', function(e) {
            const targetId = this.getAttribute('href').substring(1);

            // 可以在这里添加步骤切换后的逻辑
            if (typeof fillStepContent === 'function') {
                const stepNumber = parseInt(targetId.replace('step', ''));
                fillStepContent(stepNumber);
            }
        });
    });
}

// 初始化AI搜索功能 - 已移除事件绑定，由index.html处理
function initAISearch() {
    // 事件绑定已移至index.html中，避免重复绑定

}

// 处理AI搜索 - 支持模型选择
async function handleAISearch() {
    const searchInput = document.getElementById('ai-search-input');
    const resultsDiv = document.getElementById('ai-search-results');
    const contentDiv = document.getElementById('ai-analysis-content');
    const thinkingModeCheckbox = document.getElementById('thinking-mode');

    const query = searchInput.value.trim();
    if (!query) {
        showAlert('请输入故障现象描述', 'warning');
        return;
    }

    // 获取模型选择
    const selectedModel = document.querySelector('input[name="model-selection"]:checked')?.value || 'v3';
    const isThinkingMode = selectedModel === 'r1';

    // 兼容旧的thinking-mode复选框
    const thinkingMode = thinkingModeCheckbox ? thinkingModeCheckbox.checked : isThinkingMode;

    // 显示加载状态
    if (resultsDiv) {
        resultsDiv.style.display = 'block';
    }

    contentDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2 text-primary" role="status"></div>
            <span>正在分析故障现象，请稍候...</span>
        </div>
        <div class="mt-2">
            <small class="text-muted">
                <i class="bi bi-cpu"></i> 分析模式: ${thinkingMode ? 'DeepSeek-R1 (推理模式)' : 'DeepSeek-Chat (标准模式)'}
            </small>
        </div>
    `;

    try {
        // 统一使用流式响应处理 - DeepSeek-R1和DeepSeek-V3都使用流式输出
        await performStreamingAnalysisInMainJS(query, contentDiv);
        resultsDiv.style.display = 'block';

    } catch (error) {
        console.error('AI分析失败:', error);
        contentDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>分析失败</strong><br>
                ${error.message || '服务暂时不可用，请稍后重试'}
            </div>
        `;
    }
}

// 流式分析函数 - 用于main.js
async function performStreamingAnalysisInMainJS(query, contentDiv) {
    try {
        // 清空之前的内容
        contentDiv.innerHTML = `
            <div class="mb-3">
                <strong>🤖 模型:</strong> DeepSeek-R1 (深度推理模式)
            </div>
            <div class="mb-3">
                <strong>🧠 专家推理过程:</strong>
                <div class="text-muted small mb-2">正在展现电力系统专家的完整思维过程...</div>
                <div id="reasoning-stream-main" class="reasoning-process" style="
                    margin-top: 0.5rem;
                    padding: 1.2rem;
                    background-color: #f8f9fa;
                    border-left: 4px solid #6c757d;
                    font-family: 'Microsoft YaHei', sans-serif;
                    white-space: pre-wrap;
                    min-height: 100px;
                    max-height: 600px;
                    font-size: 14px;
                    line-height: 1.8;
                    border-radius: 4px;
                    overflow-y: auto;
                    color: #333;
                    border: 1px solid #dee2e6;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                ">
                    <span class="text-muted">🔍 正在连接DeepSeek-R1，准备开始深度推理...</span>
                </div>
            </div>
            <div class="mb-3" id="final-analysis-container-main" style="display: none;">
                <strong>📋 基于推理的详细诊断报告:</strong>
                <div class="text-muted small mb-2">基于上述推理过程，生成更加详细和具体的故障诊断结果</div>
                <div id="final-stream-main" style="
                    margin-top: 0.5rem;
                    padding: 1.2rem;
                    border-radius: 4px;
                    background-color: #f8f9fa;
                    border-left: 4px solid #6c757d;
                    font-family: 'Microsoft YaHei', sans-serif;
                    white-space: pre-wrap;
                    min-height: 100px;
                    max-height: 600px;
                    font-size: 14px;
                    line-height: 1.8;
                    overflow-y: auto;
                    color: #333;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                    border: 1px solid #dee2e6;
                ">
                </div>
            </div>
        `;

        const reasoningDiv = document.getElementById('reasoning-stream-main');
        const finalContainer = document.getElementById('final-analysis-container-main');
        const finalDiv = document.getElementById('final-stream-main');

        // 使用fetch的流式处理
        const response = await fetch('/api/v1/analyze_stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: query,
                thinking_mode: true
            })
        });

        if (!response.ok) {
            throw new Error('流式请求失败');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        reasoningDiv.innerHTML = '';

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop();

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        if (data.type === 'reasoning') {
                            // 推理过程内容 - 保持结构化格式
                            let cleanContent = data.content;

                            // 轻度清理：保留重要的结构化信息
                            cleanContent = cleanContent
                                .replace(/```.*?```/gs, '')      // 移除代码块
                                .replace(/[🧠📊🔍⚡💡🎯📋✅❌🔄🏁]/g, ''); // 移除表情符号

                            // 保持推理结构
                            cleanContent = cleanContent
                                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 保留粗体
                                .replace(/^#{1,3}\s+(.+)$/gm, '<h5 class="reasoning-header">$1</h5>')  // 转换标题
                                .replace(/^\s*\d+\.\s+(.+)$/gm, '<div class="reasoning-step"><span class="step-marker">$&</span></div>')  // 保留数字列表
                                .replace(/^\s*[-*+]\s+(.+)$/gm, '<div class="reasoning-point">• $1</div>')  // 保留符号列表
                                .replace(/\n\n/g, '</p><p class="reasoning-paragraph">')  // 段落分隔
                                .replace(/\n/g, '<br>');         // 行内换行

                            // 包装段落
                            if (cleanContent && !cleanContent.startsWith('<')) {
                                cleanContent = '<p class="reasoning-paragraph">' + cleanContent + '</p>';
                            }

                            reasoningDiv.innerHTML += cleanContent;
                            reasoningDiv.scrollTop = reasoningDiv.scrollHeight;
                        } else if (data.type === 'final') {
                            // 显示详细诊断报告区域
                            if (finalContainer.style.display === 'none') {
                                finalContainer.style.display = 'block';
                            }

                            // 最终详细诊断内容 - 保持结构化格式
                            let cleanFinalContent = data.content;

                            // 轻度清理：只移除过度的格式标记，保留结构
                            cleanFinalContent = cleanFinalContent
                                .replace(/```.*?```/gs, '')      // 移除代码块
                                .replace(/<think>.*?<\/think>/gs, '')  // 移除think标签
                                .replace(/<answer>(.*?)<\/answer>/gs, '$1')  // 提取answer内容
                                .replace(/[🧠📊🔍⚡💡🎯📋✅❌🔄🏁]/g, ''); // 移除表情符号

                            // 保持基本结构格式
                            cleanFinalContent = cleanFinalContent
                                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 保留粗体
                                .replace(/^#{1,3}\s+(.+)$/gm, '<h4>$1</h4>')      // 转换标题
                                .replace(/^\s*\d+\.\s+(.+)$/gm, '<div class="analysis-step"><span class="step-number">$&</span></div>')  // 保留数字列表
                                .replace(/^\s*[-*+]\s+(.+)$/gm, '<div class="analysis-point">• $1</div>')  // 保留符号列表
                                .replace(/\n\n/g, '</p><p>')     // 段落分隔
                                .replace(/\n/g, '<br>');         // 行内换行

                            // 包装段落
                            if (cleanFinalContent && !cleanFinalContent.startsWith('<')) {
                                cleanFinalContent = '<p>' + cleanFinalContent + '</p>';
                            }

                            finalDiv.innerHTML += cleanFinalContent;
                            finalDiv.scrollTop = finalDiv.scrollHeight;
                        } else if (data.type === 'complete') {

                            break;
                        } else if (data.type === 'error') {
                            reasoningDiv.innerHTML += `\n\n❌ 错误: ${data.message}`;
                            break;
                        }
                    } catch (e) {
                        console.error('解析流式数据失败:', e);
                    }
                }
            }
        }

    } catch (error) {
        console.error('流式分析失败:', error);
        contentDiv.innerHTML = `
            <div class="alert alert-danger">
                <strong>❌ 流式分析失败:</strong> ${error.message}
                <br><small>请尝试使用DeepSeek-V3模式或检查网络连接</small>
            </div>
        `;
    }
}

// displayAIAnalysisResult 函数已移除 - 统一使用流式处理

// 复制分析结果到剪贴板
function copyAnalysisResult() {
    const analysisContent = document.querySelector('.analysis-content');
    if (analysisContent) {
        navigator.clipboard.writeText(analysisContent.textContent).then(() => {
            showAlert('分析结果已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            showAlert('复制失败，请手动选择文本复制', 'warning');
        });
    }
}

// 移除复杂的复制推理过程函数

// 存储AI分析数据，用于动态填充表单
let aiAnalysisData = {
    equipmentType: '',
    faultType: '',
    currentQuery: '',
    analysisText: ''
};

// 从AI分析结果填充表单
function fillFormFromAI(equipmentType, faultType) {
    // 获取当前AI分析的完整内容
    const aiSearchInput = document.getElementById('ai-search-input');
    const currentQuery = aiSearchInput ? aiSearchInput.value : '';

    // 获取当前分析结果
    const analysisContent = document.querySelector('.pure-text-result');
    const analysisText = analysisContent ? analysisContent.textContent : '';

    // 存储数据供后续使用
    aiAnalysisData = {
        equipmentType: equipmentType,
        faultType: faultType,
        currentQuery: currentQuery,
        analysisText: analysisText
    };

    // 切换到故障分析页面
    switchTab('fault-analysis');

    // 跳转到第一个步骤（运行方式）
    setTimeout(() => {
        const firstStepTab = document.querySelector('#fault-analysis-tabs .nav-link[href="#step1"]');

        if (firstStepTab) {
            // 使用Bootstrap API激活第一步
            const tab = new window.bootstrap.Tab(firstStepTab);
            tab.show();

        }

        // 立即填充第一步的内容
        setTimeout(() => {
            fillStepContent(1);
        }, 100);
    }, 200);

    // 强制填充所有相关步骤的内容，不依赖tab切换
    setTimeout(() => {

        fillAllStepsContent();

        // 简化的直接填充方法
        setTimeout(() => {
            directFillAllFields();
        }, 500);
    }, 800);

    showAlert('已将AI分析结果应用到详细分析表单，请继续完善其他信息', 'success');
}

// 根据步骤填充对应内容
function fillStepContent(stepNumber) {

    const { equipmentType, faultType, currentQuery, analysisText } = aiAnalysisData;

    if (!currentQuery && !analysisText) {

        return;
    }

    switch(stepNumber) {
        case 1:
            // 第1步：运行方式
            const operationDescField = document.getElementById('operation-description');

            if (operationDescField && currentQuery) {
                const content = `故障现象：${currentQuery}\n\n基于AI分析的运行状态建议：\n${analysisText.substring(0, 300)}${analysisText.length > 300 ? '...' : ''}`;
                operationDescField.value = content;

            }
            break;

        case 2:
            // 第2步：设备信息
            const equipmentParamsField = document.getElementById('equipment-parameters');

            if (equipmentParamsField) {
                const content = `设备类型：${equipmentType}\n故障类型：${faultType}\n\n需要核实的技术参数：\n- 设备型号和制造年份\n- 额定容量和电压等级\n- 绝缘等级和冷却方式\n- 历史维护记录`;
                equipmentParamsField.value = content;

            }
            break;

        case 3:
            // 第3步：现场检查
            const visualInspectionField = document.getElementById('visual-inspection');

            if (visualInspectionField && currentQuery) {
                const content = `故障现象：${currentQuery}\n\n建议重点检查：\n- 设备外观是否有烧损、变形\n- 连接部位是否松动\n- 绝缘子是否有闪络痕迹`;
                visualInspectionField.value = content;

            }
            break;

        case 6:
            // 第6步：故障原因分析
            const preliminaryAnalysisField = document.getElementById('preliminary-analysis');

            if (preliminaryAnalysisField && analysisText) {
                const summary = analysisText.substring(0, 500) + (analysisText.length > 500 ? '...\n\n[完整分析请参考AI分析结果]' : '');
                const content = `基于AI智能分析的初步结论：\n\n${summary}`;
                preliminaryAnalysisField.value = content;

            }
            break;
    }
}

// 强制填充所有步骤内容 - 完全重写版本
function fillAllStepsContent() {

    const { equipmentType, faultType, currentQuery, analysisText } = aiAnalysisData;

    if (!currentQuery && !analysisText) {

        return;
    }

    // 定义所有需要填充的字段映射
    const fieldMappings = [
        {
            step: 1,
            name: '运行方式详细描述',
            id: 'operation-description',
            content: `故障现象：${currentQuery}\n\n基于AI分析的运行状态建议：\n${analysisText.substring(0, 300)}${analysisText.length > 300 ? '...' : ''}`
        },
        {
            step: 2,
            name: '设备技术参数',
            id: 'equipment-parameters',
            content: `设备类型：${equipmentType}\n故障类型：${faultType}\n\n需要核实的技术参数：\n- 设备型号和制造年份\n- 额定容量和电压等级\n- 绝缘等级和冷却方式\n- 历史维护记录`
        },
        {
            step: 3,
            name: '外观检查',
            id: 'visual-inspection',
            content: `故障现象：${currentQuery}\n\n建议重点检查：\n- 设备外观是否有烧损、变形\n- 连接部位是否松动\n- 绝缘子是否有闪络痕迹\n- 油位、油色是否正常`
        },
        {
            step: 6,
            name: '初步分析结论',
            id: 'preliminary-analysis',
            content: `基于AI智能分析的初步结论：\n\n${analysisText.substring(0, 500)}${analysisText.length > 500 ? '...\n\n[完整分析请参考AI分析结果]' : ''}`
        }
    ];

    // 逐个填充字段
    let successCount = 0;
    let failCount = 0;

    fieldMappings.forEach(mapping => {

        const field = document.getElementById(mapping.id);
        if (field) {
            // 强制显示字段所在的tab（如果需要）
            const stepTab = document.getElementById(`step${mapping.step}`);
            if (stepTab) {
                stepTab.style.display = 'block';
            }

            // 填充内容
            field.value = mapping.content;

            // 触发change事件确保内容被保存
            field.dispatchEvent(new Event('change', { bubbles: true }));
            field.dispatchEvent(new Event('input', { bubbles: true }));

            successCount++;
        } else {

            failCount++;
        }
    });

    // 额外的调试信息

}

// 全局调试函数 - 可在浏览器控制台中调用


    // 检查关键字段
    const keyFields = ['operation-description', 'equipment-parameters', 'visual-inspection', 'preliminary-analysis'];

    keyFields.forEach(id => {
        const field = document.getElementById(id);
        if (field) {

        } else {

        }
    });

    // 检查tab状态

    for (let i = 1; i <= 7; i++) {
        const tab = document.getElementById(`step${i}`);
        if (tab) {
            const isActive = tab.classList.contains('active');
            const isVisible = tab.offsetParent !== null;

        }
    }

    return '调试信息已输出到控制台';


// 全局测试填充函数

    // 执行填充
    fillAllStepsContent();

    return '测试填充已执行，请检查各步骤内容';
function directFillAllFields() {

    // 详细检查AI数据

    const { equipmentType, faultType, currentQuery, analysisText } = aiAnalysisData || {};

    // 如果没有数据，使用测试数据
    let testData = {
        equipmentType: equipmentType || '变压器',
        faultType: faultType || '套管故障',
        currentQuery: currentQuery || '110kV变压器套管爆炸，现场有明火和浓烟',
        analysisText: analysisText || '基于故障现象分析，初步判断为套管绝缘击穿导致的爆炸事故。需要立即切断电源，组织灭火，并进行详细的现场勘查。'
    };

    // 定义要填充的字段 - 使用测试数据
    const fields = [
        {
            id: 'operation-description',
            content: `故障现象：${testData.currentQuery}\n\n基于AI分析的运行状态建议：\n${testData.analysisText.substring(0, 300)}${testData.analysisText.length > 300 ? '...' : ''}`,
            name: '运行方式详细描述'
        },
        {
            id: 'equipment-parameters',
            content: `设备类型：${testData.equipmentType}\n故障类型：${testData.faultType}\n\n需要核实的技术参数：\n- 设备型号和制造年份\n- 额定容量和电压等级\n- 绝缘等级和冷却方式\n- 历史维护记录`,
            name: '设备技术参数'
        },
        {
            id: 'visual-inspection',
            content: `故障现象：${testData.currentQuery}\n\n建议重点检查：\n- 设备外观是否有烧损、变形\n- 连接部位是否松动\n- 绝缘子是否有闪络痕迹\n- 油位、油色是否正常`,
            name: '外观检查'
        },
        {
            id: 'preliminary-analysis',
            content: `基于AI智能分析的初步结论：\n\n${testData.analysisText.substring(0, 500)}${testData.analysisText.length > 500 ? '...\n\n[完整分析请参考AI分析结果]' : ''}`,
            name: '初步分析结论'
        }
    ];

    let successCount = 0;
    let failCount = 0;

    // 最简单的填充方式 - 直接设置值，不管可见性
    fields.forEach(fieldInfo => {
        try {
            const field = document.getElementById(fieldInfo.id);

            if (!field) {

                failCount++;
                return;
            }

            // 直接设置值
            field.value = fieldInfo.content;

            // 触发事件确保值被保存
            field.dispatchEvent(new Event('input', { bubbles: true }));
            field.dispatchEvent(new Event('change', { bubbles: true }));

            // 自动调整高度
            field.style.height = 'auto';
            field.style.height = field.scrollHeight + 'px';

            // 验证填充结果
            const actualValue = field.value;
            const isSuccess = actualValue === fieldInfo.content;

            if (isSuccess) {
                successCount++;

                // 简单的视觉反馈
                field.style.backgroundColor = '#d4edda';
                field.style.border = '2px solid #28a745';

                setTimeout(() => {
                    field.style.backgroundColor = '';
                    field.style.border = '';
                }, 2000);

            } else {
                failCount++;

            }

        } catch (error) {

            failCount++;
        }
    });

    // 给tab添加成功标记
    if (successCount > 0) {
        const tabsToMark = [
            { href: '#step1', hasContent: !!document.getElementById('operation-description')?.value },
            { href: '#step2', hasContent: !!document.getElementById('equipment-parameters')?.value },
            { href: '#step3', hasContent: !!document.getElementById('visual-inspection')?.value },
            { href: '#step6', hasContent: !!document.getElementById('preliminary-analysis')?.value }
        ];

        tabsToMark.forEach(tab => {
            if (tab.hasContent) {
                const tabLink = document.querySelector(`a[href="${tab.href}"]`);
                if (tabLink) {
                    const existingMark = tabLink.querySelector('.success-mark');
                    if (existingMark) existingMark.remove();

                    const mark = document.createElement('span');
                    mark.className = 'success-mark';
                    mark.innerHTML = ' ✅';
                    mark.style.color = '#28a745';
                    tabLink.appendChild(mark);
                }
            }
        });

        showNotification(`✅ 已成功填充 ${successCount} 个字段，请点击各步骤查看内容！`, 'success');
    }
}

// 独立的测试填充函数
;

    // 调用填充函数
    directFillAllFields();

    // 显示提示
    showNotification('🧪 测试数据已设置，正在填充字段...', 'info');

// 显示通知函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 强制可见填充 - 通过激活每个tab来确保填充
function forceVisibleFill() {

    const { equipmentType, faultType, currentQuery, analysisText } = aiAnalysisData;

    if (!currentQuery && !analysisText) {

        return;
    }

    // 定义需要填充的步骤和内容
    const fillTasks = [
        {
            step: 1,
            tabId: 'step1',
            fieldId: 'operation-description',
            content: `故障现象：${currentQuery}\n\n基于AI分析的运行状态建议：\n${analysisText.substring(0, 300)}${analysisText.length > 300 ? '...' : ''}`
        },
        {
            step: 2,
            tabId: 'step2',
            fieldId: 'equipment-parameters',
            content: `设备类型：${equipmentType}\n故障类型：${faultType}\n\n需要核实的技术参数：\n- 设备型号和制造年份\n- 额定容量和电压等级\n- 绝缘等级和冷却方式\n- 历史维护记录`
        },
        {
            step: 3,
            tabId: 'step3',
            fieldId: 'visual-inspection',
            content: `故障现象：${currentQuery}\n\n建议重点检查：\n- 设备外观是否有烧损、变形\n- 连接部位是否松动\n- 绝缘子是否有闪络痕迹\n- 油位、油色是否正常`
        },
        {
            step: 6,
            tabId: 'step6',
            fieldId: 'preliminary-analysis',
            content: `基于AI智能分析的初步结论：\n\n${analysisText.substring(0, 500)}${analysisText.length > 500 ? '...\n\n[完整分析请参考AI分析结果]' : ''}`
        }
    ];

    // 逐个激活tab并填充
    let currentIndex = 0;

    function fillNextStep() {
        if (currentIndex >= fillTasks.length) {

            // 回到第一步
            const firstTab = document.querySelector('#fault-analysis-tabs .nav-link[href="#step1"]');
            if (firstTab) {
                const tab = new window.bootstrap.Tab(firstTab);
                tab.show();
            }
            return;
        }

        const task = fillTasks[currentIndex];

        // 激活对应的tab
        const tabLink = document.querySelector(`#fault-analysis-tabs .nav-link[href="#${task.tabId}"]`);
        if (tabLink) {
            const tab = new window.bootstrap.Tab(tabLink);
            tab.show();

            // 等待tab切换完成后填充
            setTimeout(() => {
                const field = document.getElementById(task.fieldId);
                if (field) {
                    field.value = task.content;
                    field.dispatchEvent(new Event('change', { bubbles: true }));
                    field.dispatchEvent(new Event('input', { bubbles: true }));

                } else {

                }

                currentIndex++;
                // 继续下一个步骤
                setTimeout(fillNextStep, 300);
            }, 200);
        } else {

            currentIndex++;
            setTimeout(fillNextStep, 100);
        }
    }

    // 开始填充
    fillNextStep();
}

// 智能填充当前步骤
function smartFillCurrentStep(stepNumber) {

    const { equipmentType, faultType, currentQuery, analysisText } = aiAnalysisData;

    if (!currentQuery && !analysisText) {

        return;
    }

    // 定义步骤映射
    const stepMappings = {
        1: {
            fieldId: 'operation-description',
            content: `故障现象：${currentQuery}\n\n基于AI分析的运行状态建议：\n${analysisText.substring(0, 300)}${analysisText.length > 300 ? '...' : ''}`
        },
        2: {
            fieldId: 'equipment-parameters',
            content: `设备类型：${equipmentType}\n故障类型：${faultType}\n\n需要核实的技术参数：\n- 设备型号和制造年份\n- 额定容量和电压等级\n- 绝缘等级和冷却方式\n- 历史维护记录`
        },
        3: {
            fieldId: 'visual-inspection',
            content: `故障现象：${currentQuery}\n\n建议重点检查：\n- 设备外观是否有烧损、变形\n- 连接部位是否松动\n- 绝缘子是否有闪络痕迹\n- 油位、油色是否正常`
        },
        6: {
            fieldId: 'preliminary-analysis',
            content: `基于AI智能分析的初步结论：\n\n${analysisText.substring(0, 500)}${analysisText.length > 500 ? '...\n\n[完整分析请参考AI分析结果]' : ''}`
        }
    };

    const mapping = stepMappings[stepNumber];
    if (mapping) {
        const field = document.getElementById(mapping.fieldId);
        if (field) {
            // 只有当字段为空时才填充，避免覆盖用户输入
            if (field.value.trim() === '') {
                field.value = mapping.content;
                field.dispatchEvent(new Event('change', { bubbles: true }));
                field.dispatchEvent(new Event('input', { bubbles: true }));

                // 视觉提示
                field.style.backgroundColor = '#e8f5e8';
                setTimeout(() => {
                    field.style.backgroundColor = '';
                }, 2000);
            } else {

            }
        } else {

        }
    } else {

    }
}

// 故障分析处理
async function handleFaultAnalysis(e) {
    e.preventDefault();
    
    const formData = {
        equipment_info: document.getElementById('equipment-info').value,
        fault_symptoms: document.getElementById('fault-symptoms').value,
        inspection_results: document.getElementById('inspection-results').value,
        historical_data: document.getElementById('historical-data').value,
        analysis_type: document.getElementById('analysis-type').value
    };
    
    // 验证表单
    if (!formData.equipment_info || !formData.fault_symptoms) {
        showAlert('请填写设备信息和故障现象', 'warning');
        return;
    }
    
    // 显示加载状态
    showAnalysisLoading();
    
    try {
        const response = await fetch(`${API_BASE_URL}/fault/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        displayAnalysisResult(result);
        
        // 添加到历史记录
        addToAnalysisHistory(result);
        
    } catch (error) {
        console.error('故障分析失败:', error);
        showAlert('分析失败，请稍后重试', 'danger');
        hideAnalysisLoading();
    }
}

// 显示分析加载状态
function showAnalysisLoading() {
    const placeholder = safeGetElement('analysis-placeholder', true);
    const results = safeGetElement('analysis-results', true);
    const loading = safeGetElement('analysis-loading', true);

    safeClassListOperation(placeholder, 'add', 'd-none');
    safeClassListOperation(results, 'add', 'd-none');
    safeClassListOperation(loading, 'remove', 'd-none');
}

// 隐藏分析加载状态
function hideAnalysisLoading() {
    const loading = safeGetElement('analysis-loading', true);
    const placeholder = safeGetElement('analysis-placeholder', true);

    safeClassListOperation(loading, 'add', 'd-none');
    safeClassListOperation(placeholder, 'remove', 'd-none');
}

// 显示分析结果
function displayAnalysisResult(result) {
    hideAnalysisLoading();

    // 显示结果区域
    const resultsElement = safeGetElement('analysis-results', true);
    const placeholder = safeGetElement('analysis-placeholder', true);

    safeClassListOperation(resultsElement, 'remove', 'd-none');
    safeClassListOperation(placeholder, 'add', 'd-none');

    // 填充结果内容
    const faultCauses = safeGetElement('fault-causes', true);
    const faultLogic = safeGetElement('fault-logic', true);
    const recommendations = safeGetElement('recommendations', true);

    if (faultCauses) faultCauses.textContent = result.fault_causes || '暂无分析结果';
    if (faultLogic) faultLogic.textContent = result.fault_logic || '暂无逻辑链条';
    if (recommendations) recommendations.textContent = result.recommendations || '暂无处理建议';

    // 添加动画效果
    safeClassListOperation(resultsElement, 'add', 'fade-in');
}

// 添加到分析历史
function addToAnalysisHistory(result) {
    const historyItem = {
        id: Date.now(),
        timestamp: new Date().toLocaleString(),
        equipment_info: result.equipment_info || '未知设备',
        fault_symptoms: result.fault_symptoms || '未知故障',
        result: result
    };
    
    analysisHistory.unshift(historyItem);
    updateAnalysisHistoryDisplay();
}

// 更新分析历史显示
function updateAnalysisHistoryDisplay() {
    const historyContainer = document.getElementById('analysis-history');
    
    if (analysisHistory.length === 0) {
        historyContainer.innerHTML = '<p class="text-muted text-center">暂无分析历史</p>';
        return;
    }
    
    const historyHTML = analysisHistory.slice(0, 10).map(item => `
        <div class="list-group-item list-group-item-action" onclick="loadHistoryItem(${item.id})">
            <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">${item.equipment_info}</h6>
                <small>${item.timestamp}</small>
            </div>
            <p class="mb-1">${item.fault_symptoms}</p>
        </div>
    `).join('');
    
    historyContainer.innerHTML = historyHTML;
}

// 加载历史记录项
function loadHistoryItem(id) {
    const item = analysisHistory.find(h => h.id === id);
    if (item) {
        displayAnalysisResult(item.result);
        
        // 更新表单内容
        document.getElementById('equipment-info').value = item.result.equipment_info || '';
        document.getElementById('fault-symptoms').value = item.result.fault_symptoms || '';
        document.getElementById('inspection-results').value = item.result.inspection_results || '';
        document.getElementById('historical-data').value = item.result.historical_data || '';
    }
}

// 加载分析历史
async function loadAnalysisHistory() {
    try {
        const response = await fetch(`${API_BASE_URL}/fault/history`);
        if (response.ok) {
            const history = await response.json();
            analysisHistory = history.slice(0, 10); // 只保留最近10条
            updateAnalysisHistoryDisplay();
        }
    } catch (error) {
        console.error('加载分析历史失败:', error);
    }
}

// 设备管理相关函数
async function handleAddEquipment(e) {
    e.preventDefault();

    const formData = {
        name: document.getElementById('equipment-name').value,
        type: document.getElementById('equipment-type').value,
        location: document.getElementById('equipment-location').value,
        status: document.getElementById('equipment-status').value
    };

    try {
        const response = await fetch(`${API_BASE_URL}/equipment`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        showAlert('设备添加成功', 'success');

        // 清空表单
        document.getElementById('equipment-form').reset();

        // 重新加载设备列表
        loadEquipmentList();

    } catch (error) {
        console.error('添加设备失败:', error);
        showAlert('添加设备失败，请稍后重试', 'danger');
    }
}

// 加载设备列表
async function loadEquipmentList(filters = {}) {
    try {
        // 构建查询参数
        const params = new URLSearchParams();
        if (filters.search) params.append('search', filters.search);
        if (filters.type) params.append('type', filters.type);
        if (filters.status) params.append('status', filters.status);
        if (filters.location) params.append('location', filters.location);

        const url = `${API_BASE_URL}/equipment${params.toString() ? '?' + params.toString() : ''}`;
        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.equipment && Array.isArray(data.equipment)) {
            equipmentList = data.equipment;
            window.equipmentList = data.equipment; // 同步到全局变量
            window.equipmentDataTimestamp = Date.now(); // 记录加载时间

            // 更新显示
            updateEquipmentTableDisplay();
            updateEquipmentStatistics(data.statistics);

            // 更新搜索结果统计
            updateEquipmentSearchStats(data.filtered_total || data.total, data.total);

        } else {

            equipmentList = [];
            window.equipmentList = [];
            showAlert(data.error || '获取设备数据失败', 'warning');
        }

    } catch (error) {
        console.error('加载设备列表失败:', error);
        showAlert('加载设备列表失败，请检查网络连接', 'danger');
        equipmentList = [];
    }
}

// 更新设备表格显示
function updateEquipmentTableDisplay(filteredList = null) {
    const tableBody = document.getElementById('equipment-table-body');
    const list = filteredList || equipmentList;

    // 确保list是数组
    if (!Array.isArray(list)) {
        console.error('updateEquipmentTableDisplay: list不是数组', list);
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">数据格式错误</td></tr>';
        return;
    }

    if (list.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无设备数据</td></tr>';
        return;
    }

    const tableHTML = list.map(equipment => {
        const statusClass = getStatusClass(equipment.status);
        const statusText = getStatusText(equipment.status);
        const typeText = getEquipmentTypeText(equipment.type);

        return `
        <tr class="equipment-row" data-equipment-id="${equipment.id}">
            <td>
                <span class="equipment-id">${equipment.id}</span>
            </td>
            <td>
                <div class="fw-bold">${equipment.name || '未命名设备'}</div>
            </td>
            <td>
                <span class="type-badge">${typeText}</span>
            </td>
            <td>
                <i class="bi bi-geo-alt text-muted me-1"></i>${equipment.location || '未指定'}
            </td>
            <td>
                <span class="status-badge ${statusClass}">${statusText}</span>
            </td>
            <td>
                <div class="text-muted small">
                    ${formatDateTime(equipment.updated_at) || formatDateTime(equipment.last_status_update) || '未知'}
                </div>
            </td>
            <td>
                <div class="d-flex gap-1">
                    <button class="btn-action btn-view" onclick="viewEquipmentDetail('${equipment.id}')" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn-action btn-edit" onclick="editEquipment('${equipment.id}')" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn-action btn-delete" onclick="deleteEquipment('${equipment.id}')" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
        `;
    }).join('');

    tableBody.innerHTML = tableHTML;

    // 更新搜索统计信息
    if (filteredList === null) {
        // 显示全部设备时
        updateEquipmentSearchStats(list.length, list.length);
    }
}

// 废弃的函数已删除 - updateEquipmentCharts 功能已完全集成到 updateEquipmentStatistics 中

// 设备搜索处理
function handleEquipmentSearch(e) {
    const searchTerm = e.target.value.toLowerCase();

    if (!searchTerm) {
        updateEquipmentTableDisplay();
        updateEquipmentSearchStats(equipmentList.length, equipmentList.length);
        return;
    }

    // 确保equipmentList是数组
    if (!Array.isArray(equipmentList)) {
        console.error('handleEquipmentSearch: equipmentList不是数组', equipmentList);
        return;
    }

    const filteredList = equipmentList.filter(equipment =>
        equipment.name.toLowerCase().includes(searchTerm) ||
        equipment.location.toLowerCase().includes(searchTerm) ||
        getEquipmentTypeText(equipment.type).toLowerCase().includes(searchTerm)
    );

    updateEquipmentTableDisplay(filteredList);
    updateEquipmentSearchStats(filteredList.length, equipmentList.length);
}

// 更新设备搜索统计信息
function updateEquipmentSearchStats(filteredCount, totalCount) {
    const statsElement = document.getElementById('equipment-search-stats');
    if (statsElement) {
        if (filteredCount === totalCount) {
            statsElement.textContent = `显示全部 ${totalCount} 个设备`;
        } else {
            statsElement.textContent = `找到 ${filteredCount} 个设备，共 ${totalCount} 个`;
        }
    }
}

// 知识库搜索处理 - 已移至index.html中处理，避免重复定义
// 此函数已被新的增强搜索系统替代
async function handleKnowledgeSearch_DEPRECATED(e) {
    e.preventDefault();

    const query = document.getElementById('search-query').value;
    const searchType = document.getElementById('search-type').value;
    const resultsContainer = document.getElementById('knowledge-search-results');
    const submitButton = e.target.querySelector('button[type="submit"]');

    if (!query.trim()) {
        showAlert('请输入搜索内容', 'warning');
        return;
    }

    // 显示加载状态
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="bi bi-hourglass-split"></i> 搜索中...';
    }

    if (resultsContainer) {
        resultsContainer.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">搜索中...</span>
                </div>
                <p class="mt-2 text-muted">正在搜索知识库...</p>
            </div>
        `;
    }

    try {

        const response = await fetch(`${API_BASE_URL}/knowledge/search`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: query,
                search_type: searchType,
                top_k: 10
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const results = await response.json();

        // 使用新的搜索系统，不再调用旧函数

    } catch (error) {
        console.error('知识库搜索失败:', error);
        showAlert('搜索失败，请稍后重试', 'danger');

        // 显示错误状态
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="text-center text-danger">
                    <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                    <p class="mt-2">搜索失败，请稍后重试</p>
                    <small class="text-muted">${error.message}</small>
                </div>
            `;
        }
    } finally {
        // 恢复按钮状态
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-search"></i> 搜索';
        }
    }
}

// 显示知识库搜索结果 - 已移至index.html中处理
// 此函数已被新的增强搜索系统替代
function displayKnowledgeSearchResults_DEPRECATED(results) {
    const resultsContainer = document.getElementById('knowledge-search-results');

    if (!resultsContainer) {
        console.error('未找到搜索结果容器元素');
        return;
    }

    // 检查结果格式
    if (!results || typeof results !== 'object') {
        console.error('搜索结果格式错误:', results);
        resultsContainer.innerHTML = `
            <div class="text-center text-danger">
                <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                <p class="mt-2">搜索结果格式错误</p>
            </div>
        `;
        return;
    }

    // 检查是否有结果
    if (!results.results || !Array.isArray(results.results) || results.results.length === 0) {
        resultsContainer.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-search" style="font-size: 3rem;"></i>
                <p class="mt-2">未找到相关内容</p>
                <small class="text-muted">尝试使用不同的关键词搜索</small>
            </div>
        `;
        return;
    }

    // 显示搜索统计
    const statsHTML = `
        <div class="alert alert-info mb-3">
            <i class="bi bi-info-circle"></i>
            找到 <strong>${results.results.length}</strong> 条结果
            ${results.total && results.total !== results.results.length ? `，共 ${results.total} 条匹配` : ''}
        </div>
    `;

    const resultsHTML = results.results.map((result, index) => `
        <div class="card mb-2 search-result-item" onclick="showKnowledgeDetail(${index})" style="cursor: pointer;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="card-title mb-1">
                            <i class="bi bi-file-text text-primary me-2"></i>
                            ${result.title || '未知标题'}
                        </h6>
                        <p class="card-text text-muted small mb-2">
                            ${formatContentForDisplay(result.content || result.text || '无内容', true)}
                        </p>
                        <div class="d-flex flex-wrap gap-1">
                            ${result.score ? `<span class="badge bg-success">相似度: ${(result.score * 100).toFixed(1)}%</span>` : ''}
                            <span class="badge bg-info">类型: ${result.type || '文本'}</span>
                            <span class="badge bg-secondary">来源: ${result.source || '未知'}</span>
                        </div>
                    </div>
                    <i class="bi bi-arrow-right-circle text-muted ms-2"></i>
                </div>
            </div>
        </div>
    `).join('');

    // 存储搜索结果供详情查看使用
    window.currentSearchResults = results.results;

    resultsContainer.innerHTML = statsHTML + resultsHTML;

}

// 显示知识库详情 - 简化版本
window.showKnowledgeDetail = async function(index) {

    if (!window.currentSearchResults || !window.currentSearchResults[index]) {
        showAlert('无法获取详情信息', 'warning');
        return;
    }

    const result = window.currentSearchResults[index];
    console.log('🔍 调试信息 - 搜索结果:', result);
    console.log('🔍 调试信息 - result.id:', result.id);
    console.log('🔍 调试信息 - result.source:', result.source);

    // 移除已存在的模态框
    const existingModal = document.getElementById('knowledgeDetailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建简单的模态框
    const modalHTML = `
        <div class="modal fade" id="knowledgeDetailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-file-text text-primary me-2"></i>
                            ${result.title || '知识库详情'}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="modalBodyContent">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-3">正在获取完整内容...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new window.bootstrap.Modal(document.getElementById('knowledgeDetailModal'));
    modal.show();

    // 获取完整内容
    let fullDocument = null;
    let errorMessage = null;

    if (result.id || result.source) {

        try {
            // 简单的API调用 - 修复路径匹配问题，优先使用result.id
            const docId = result.id || result.source;
            const apiUrl = `/api/v1/knowledge/document/detail/${docId}`;

            const response = await fetch(apiUrl);

            if (response.ok) {
                const data = await response.json();

                if (data.success && data.document) {
                    fullDocument = data.document;

                } else {
                    errorMessage = data.error || '获取文档失败';

                }
            } else {
                errorMessage = `HTTP错误: ${response.status}`;
                console.error('❌ HTTP错误:', response.status);
            }
        } catch (error) {
            errorMessage = `网络错误: ${error.message}`;
            console.error('❌ 网络错误:', error);
        }
    } else {

    }

    // 准备显示的内容
    const displayDocument = fullDocument || {
        title: result.title,
        content: result.content || result.text || '无内容',
        type: result.type,
        metadata: result.metadata || {}
    };

    const isComplete = !!fullDocument;
    const searchContentLength = result.content?.length || 0;
    const fullContentLength = displayDocument.content?.length || 0;

    // 检查是否为图片类型
    const isImageResult = result.type === 'image_result' || displayDocument.type === 'image_result';
    const imageUrl = result.image_url || displayDocument.image_url || result.metadata?.image_path || displayDocument.metadata?.image_path;
    const isProcessed = result.processed || displayDocument.processed || result.metadata?.processed || displayDocument.metadata?.processed;

    // 构建显示内容
    let contentHTML = '';

    if (isImageResult && imageUrl) {
        // 图片显示模式
        contentHTML = `
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">
                        <i class="bi bi-image me-2"></i>
                        ${isProcessed ? '📊 已处理图片' : '📷 原始图片'}
                        <span class="badge ${isProcessed ? 'bg-success' : 'bg-secondary'} ms-2">
                            ${isProcessed ? '标注清洗转换' : '原始数据'}
                        </span>
                    </h6>
                </div>

                ${errorMessage ? `
                    <div class="alert alert-warning py-2 mb-2">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <small>获取完整内容失败: ${errorMessage}</small>
                    </div>
                ` : ''}

                <!-- 图片显示区域 -->
                <div class="text-center mb-3">
                    <div class="border rounded p-2" style="background-color: #f8f9fa;">
                        <img src="${imageUrl}"
                             alt="${displayDocument.title || '图片'}"
                             class="img-fluid rounded shadow-sm"
                             style="max-height: 400px; max-width: 100%; object-fit: contain;"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <div style="display: none; padding: 40px; color: #6c757d;">
                            <i class="bi bi-image-fill" style="font-size: 48px;"></i>
                            <p class="mt-2">图片加载失败</p>
                            <small>路径: ${imageUrl}</small>
                        </div>
                    </div>
                </div>

                <!-- 图片信息 -->
                <div class="border rounded p-3" style="background-color: #f8f9fa;">
                    <pre style="white-space: pre-wrap; margin: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 14px; line-height: 1.5;">${formatContentForDisplay(displayDocument.content, false)}</pre>
                </div>
            </div>`;
    } else {
        // 文本显示模式
        contentHTML = `
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">
                        <i class="bi bi-file-text me-2"></i>
                        ${isComplete ? '完整内容' : '内容预览'}
                        <span class="badge ${isComplete ? 'bg-success' : 'bg-warning'} ms-2">
                            ${isComplete ? '完整版' : '预览版'}
                        </span>
                    </h6>
                    <span class="text-muted">${fullContentLength} 字符</span>
                </div>

                ${errorMessage ? `
                    <div class="alert alert-warning py-2 mb-2">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <small>获取完整内容失败: ${errorMessage}</small>
                    </div>
                ` : ''}

                ${!isComplete && !errorMessage ? `
                    <div class="alert alert-info py-2 mb-2">
                        <i class="bi bi-info-circle me-2"></i>
                        <small>显示搜索结果预览，内容可能被截断。</small>
                    </div>
                ` : ''}

                <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto; background-color: #f8f9fa;">
                    <pre style="white-space: pre-wrap; margin: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 14px; line-height: 1.5;">${formatContentForDisplay(displayDocument.content, false)}</pre>
                </div>
            </div>`;
    }

    contentHTML += `

        ${displayDocument.metadata && Object.keys(displayDocument.metadata).length > 0 ? `
            <div class="mb-3">
                <h6><i class="bi bi-info-circle me-2"></i>元数据</h6>
                <div class="row">
                    ${Object.entries(displayDocument.metadata).map(([key, value]) => `
                        <div class="col-md-6 mb-2">
                            <small class="text-muted">${key}:</small><br>
                            <span class="fw-medium">${value}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        ` : ''}

        <div class="mb-3">
            <h6><i class="bi bi-gear me-2"></i>技术信息</h6>
            <div class="row">
                <div class="col-md-4">
                    <small class="text-muted">文档类型:</small><br>
                    <span class="badge bg-info">${displayDocument.type || result.type}</span>
                </div>
                <div class="col-md-4">
                    <small class="text-muted">来源:</small><br>
                    <code style="font-size: 12px;">${result.source || '未知'}</code>
                </div>
                <div class="col-md-4">
                    <small class="text-muted">相似度:</small><br>
                    <span class="badge bg-success">${((result.score || 0) * 100).toFixed(1)}%</span>
                </div>
            </div>
        </div>

        <div class="text-center">
            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="copyFullContent()">
                <i class="bi bi-clipboard"></i> 复制内容
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="downloadDocument('${result.source}', '${displayDocument.title}')">
                <i class="bi bi-download"></i> 下载文档
            </button>
        </div>
    `;

    // 更新模态框内容
    document.getElementById('modalBodyContent').innerHTML = contentHTML;

}

// 格式化内容为非结构化文本显示
function formatContentForDisplay(content, isPreview = false) {
    if (!content) return '无内容';

    // 检测是否为结构化数据（如CSV转换的表格数据）
    const isStructuredData = content.includes('数据表:') ||
                             content.includes('行数:') ||
                             content.includes('列数:') ||
                             content.includes('列名:') ||
                             /^\s*\w+\s+\w+\s+\w+/.test(content); // 检测表格格式

    if (isStructuredData) {
        return formatStructuredDataToText(content, isPreview);
    }

    // 处理其他结构化标记
    let formattedContent = content;

    // 移除多余的换行符
    formattedContent = formattedContent.replace(/\n{3,}/g, '\n\n');

    // 将标题标记转换为更自然的文本
    formattedContent = formattedContent.replace(/^#{1,6}\s+(.+)$/gm, '$1');

    // 将列表标记转换为更自然的文本
    formattedContent = formattedContent.replace(/^[-*+]\s+(.+)$/gm, '• $1');

    // 移除粗体标记但保留内容
    formattedContent = formattedContent.replace(/\*\*(.+?)\*\*/g, '$1');

    // 如果是预览模式，限制长度并添加省略号
    if (isPreview && formattedContent.length > 300) {
        formattedContent = formattedContent.substring(0, 300) + '...';
    }

    return formattedContent;
}

// 将结构化数据转换为易读的文本格式
function formatStructuredDataToText(content, isPreview = false) {
    try {
        const lines = content.split('\n');
        let formattedText = '';
        let inTableData = false;
        let tableHeaders = [];
        let tableRows = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            if (line.startsWith('数据表:')) {
                const tableName = line.replace('数据表:', '').trim();
                formattedText += `这是一个名为"${tableName}"的数据表。\n\n`;
            } else if (line.startsWith('行数:')) {
                const match = line.match(/行数:\s*(\d+),\s*列数:\s*(\d+)/);
                if (match) {
                    formattedText += `该表包含${match[1]}行数据，共有${match[2]}个字段。\n`;
                }
            } else if (line.startsWith('列名:')) {
                const columns = line.replace('列名:', '').trim();
                formattedText += `数据字段包括：${columns}。\n\n`;
                formattedText += '数据内容如下：\n';
            } else if (line === '' && !inTableData) {
                // 空行，可能是表格数据开始
                inTableData = true;
            } else if (inTableData && line) {
                // 处理表格数据
                if (tableHeaders.length === 0) {
                    // 第一行是表头
                    tableHeaders = line.split(/\s{2,}/).filter(h => h.trim());
                } else {
                    // 数据行
                    const rowData = line.split(/\s{2,}/).filter(d => d.trim());
                    if (rowData.length > 0) {
                        tableRows.push(rowData);
                    }
                }
            }
        }

        // 将表格数据转换为自然语言描述
        if (tableRows.length > 0) {
            const maxRows = isPreview ? 3 : Math.min(tableRows.length, 10);

            for (let i = 0; i < maxRows; i++) {
                const row = tableRows[i];
                let rowDescription = `第${i + 1}条记录：`;

                for (let j = 0; j < Math.min(row.length, tableHeaders.length); j++) {
                    if (j > 0) rowDescription += '，';
                    rowDescription += `${tableHeaders[j]}为${row[j]}`;
                }

                formattedText += rowDescription + '。\n';
            }

            if (tableRows.length > maxRows) {
                formattedText += `\n（还有${tableRows.length - maxRows}条记录...）`;
            }
        }

        return formattedText || content;

    } catch (error) {

        // 回退到简单的文本清理
        return content.replace(/\s{2,}/g, ' ').replace(/\n{3,}/g, '\n\n');
    }
}

// 复制内容到剪贴板的辅助函数
window.copyFullContent = function() {
    const contentElement = document.querySelector('#knowledgeDetailModal pre');
    if (contentElement) {
        navigator.clipboard.writeText(contentElement.textContent).then(() => {
            showAlert('内容已复制到剪贴板', 'success');
        }).catch(() => {
            showAlert('复制失败', 'error');
        });
    }
}

// 获取文档类型图标
function getDocumentIcon(type) {
    const iconMap = {
        'document': 'file-text',
        'uploaded_file': 'cloud-upload',
        'uploaded_file_content': 'file-earmark-text',
        'case_study': 'journal-text',
        'fault_pattern': 'exclamation-triangle',
        'image': 'image',
        'pdf': 'file-pdf',
        'word': 'file-word',
        'excel': 'file-excel',
        'text': 'file-text'
    };
    return iconMap[type] || 'file-text';
}

// 获取文档类型标签
function getDocumentTypeLabel(type) {
    const labelMap = {
        'document': '文档',
        'uploaded_file': '上传文件',
        'uploaded_file_content': '文件内容',
        'case_study': '案例研究',
        'fault_pattern': '故障模式',
        'image': '图像',
        'pdf': 'PDF文档',
        'word': 'Word文档',
        'excel': 'Excel表格',
        'text': '文本文件'
    };
    return labelMap[type] || '未知类型';
}

// 获取来源标签
function getSourceLabel(source) {
    if (!source) return '未知来源';

    if (source.startsWith('uploads/')) {
        return '用户上传';
    } else if (source.startsWith('case_studies/')) {
        return '案例库';
    } else if (source.startsWith('fault_patterns/')) {
        return '故障库';
    } else if (source.includes('knowledge_base')) {
        return '知识库';
    }

    // 截断过长的来源路径
    return source.length > 20 ? source.substring(0, 20) + '...' : source;
}

// 渲染结构化元数据
function renderStructuredMetadata(metadata) {
    if (!metadata || typeof metadata !== 'object') {
        return '';
    }

    let metadataHTML = `
        <div class="mb-4">
            <h6><i class="bi bi-info-circle me-2"></i>详细信息</h6>
            <div class="row">
    `;

    // 处理不同类型的元数据
    const metadataItems = [];

    // 文件相关信息
    if (metadata.file_size !== undefined) {
        metadataItems.push({
            label: '文件大小',
            value: metadata.readable_size || `${metadata.file_size} 字节`,
            icon: 'hdd'
        });
    }

    if (metadata.file_type) {
        metadataItems.push({
            label: '文件类型',
            value: metadata.file_type.toUpperCase(),
            icon: 'file-earmark'
        });
    }

    // 时间信息
    if (metadata.upload_time) {
        metadataItems.push({
            label: '上传时间',
            value: formatDateTime(metadata.upload_time),
            icon: 'clock'
        });
    }

    if (metadata.created_time) {
        metadataItems.push({
            label: '创建时间',
            value: formatDateTime(metadata.created_time),
            icon: 'calendar-plus'
        });
    }

    if (metadata.modified_time) {
        metadataItems.push({
            label: '修改时间',
            value: formatDateTime(metadata.modified_time),
            icon: 'calendar-check'
        });
    }

    // 故障相关信息
    if (metadata.severity) {
        metadataItems.push({
            label: '严重程度',
            value: metadata.severity,
            icon: 'exclamation-triangle',
            badge: getSeverityBadgeClass(metadata.severity)
        });
    }

    if (metadata.category) {
        metadataItems.push({
            label: '分类',
            value: metadata.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
            icon: 'tags'
        });
    }

    // 统计信息
    if (metadata.symptoms_count !== undefined) {
        metadataItems.push({
            label: '症状数量',
            value: `${metadata.symptoms_count} 个`,
            icon: 'list-ul'
        });
    }

    if (metadata.causes_count !== undefined) {
        metadataItems.push({
            label: '原因数量',
            value: `${metadata.causes_count} 个`,
            icon: 'question-circle'
        });
    }

    if (metadata.actions_count !== undefined) {
        metadataItems.push({
            label: '处理措施',
            value: `${metadata.actions_count} 个`,
            icon: 'gear'
        });
    }

    // 其他信息
    if (metadata.source_type) {
        metadataItems.push({
            label: '数据来源',
            value: metadata.source_type,
            icon: 'database'
        });
    }

    if (metadata.data_source) {
        metadataItems.push({
            label: '数据源',
            value: metadata.data_source,
            icon: 'server'
        });
    }

    // 渲染元数据项
    metadataItems.forEach((item, index) => {
        if (index % 2 === 0 && index > 0) {
            metadataHTML += `</div><div class="row">`;
        }

        metadataHTML += `
            <div class="col-md-6 mb-2">
                <div class="d-flex align-items-center">
                    <i class="bi bi-${item.icon} text-muted me-2"></i>
                    <small class="text-muted me-2">${item.label}:</small>
                    ${item.badge ?
                        `<span class="badge ${item.badge}">${item.value}</span>` :
                        `<span class="fw-bold">${item.value}</span>`
                    }
                </div>
            </div>
        `;
    });

    metadataHTML += `
            </div>
        </div>
    `;

    return metadataHTML;
}

// 格式化日期时间
function formatDateTime(dateString) {
    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        return dateString;
    }
}

// 获取严重程度徽章样式
function getSeverityBadgeClass(severity) {
    const severityMap = {
        '高': 'bg-danger',
        '中': 'bg-warning',
        '低': 'bg-success',
        'high': 'bg-danger',
        'medium': 'bg-warning',
        'low': 'bg-success',
        '严重': 'bg-danger',
        '一般': 'bg-warning',
        '轻微': 'bg-success'
    };
    return severityMap[severity] || 'bg-secondary';
}

// 下载文档
window.downloadDocument = function(source, title) {
    if (!source || !source.startsWith('uploads/')) {
        showAlert('无法下载此文档', 'warning');
        return;
    }

    // 创建下载链接
    const downloadUrl = `${API_BASE_URL}/../${source}`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = source.split('/').pop();
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showAlert('开始下载文档', 'success');
}

// 复制知识库内容
function copyKnowledgeContent(index) {
    if (!window.currentSearchResults || !window.currentSearchResults[index]) {
        showAlert('无法复制内容', 'warning');
        return;
    }

    const result = window.currentSearchResults[index];
    const content = `标题: ${result.title || '未知标题'}\n\n内容:\n${result.content || result.text || '无内容'}`;

    navigator.clipboard.writeText(content).then(() => {
        showAlert('内容已复制到剪贴板', 'success');
    }).catch(() => {
        showAlert('复制失败，请手动复制', 'warning');
    });
}

// 文件上传处理
async function uploadFiles() {
    const fileInput = document.getElementById('file-input');
    const uploadType = document.getElementById('upload-type').value;

    if (!fileInput.files.length) {
        showAlert('请选择要上传的文件', 'warning');
        return;
    }

    const formData = new FormData();

    // 添加文件 - 修复字段名称匹配服务器端期望
    if (fileInput.files.length === 1) {
        // 单文件上传使用 'file' 字段
        formData.append('file', fileInput.files[0]);
    } else {
        // 多文件上传使用 'files' 字段
        for (let i = 0; i < fileInput.files.length; i++) {
            formData.append('files', fileInput.files[i]);
        }
    }

    try {
        // 修复API端点 - 使用正确的后端路由
        let endpoint = `${API_BASE_URL}/upload-document`;

        // 显示上传进度
        showAlert('正在上传文件，请稍候...', 'info');

        const response = await fetch(endpoint, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ 服务器错误响应:', errorText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        const results = await response.json();

        // 显示上传结果
        displayUploadResults(results, uploadType);

        // 显示成功消息
        showAlert('文件上传成功！', 'success');

        // 清空文件选择
        fileInput.value = '';

    } catch (error) {
        console.error('❌ 文件上传失败:', error);
        showAlert(`文件上传失败: ${error.message}`, 'danger');

        // 显示错误详情
        displayUploadError(error);
    }
}

// 显示上传结果
function displayUploadResults(results, uploadType = 'document') {
    const resultsContainer = document.getElementById('upload-results');

    if (!results) {
        resultsContainer.innerHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i>
                上传处理失败或无结果
            </div>
        `;
        return;
    }

    let resultsHTML = '';

    // 处理我们后端返回的格式 (success: true, file_info: {...})
    if (results.success && results.file_info) {
        const fileInfo = results.file_info;
        resultsHTML = `
            <div class="alert alert-success mb-3">
                <i class="bi bi-check-circle"></i>
                ${results.message || '文件上传成功'}
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-file-earmark-text text-primary me-2" style="font-size: 1.5rem;"></i>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${fileInfo.original_name}</h6>
                            <small class="text-muted">保存为: ${fileInfo.saved_name}</small>
                        </div>
                        <span class="badge bg-success">✓ 已上传</span>
                    </div>
                    <div class="row g-2">
                        <div class="col-6">
                            <small class="text-muted d-block">文件大小</small>
                            <strong>${(fileInfo.file_size / 1024).toFixed(1)} KB</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">文件类型</small>
                            <strong>${fileInfo.file_type}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">上传时间</small>
                            <strong>${new Date(fileInfo.upload_time).toLocaleString()}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">存储路径</small>
                            <strong title="${fileInfo.file_path}">已保存</strong>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            文件已成功上传到服务器，可以进行后续处理。
                        </small>
                    </div>
                </div>
            </div>
        `;
    }

    // 处理批量上传结果 (upload_results数组)
    if (results.upload_results && Array.isArray(results.upload_results)) {
        resultsHTML = `
            <div class="alert alert-success mb-3">
                <i class="bi bi-check-circle"></i>
                ${results.message || '批量上传成功'}
            </div>
            <div class="upload-files-list">
                ${results.upload_results.map(file => `
                    <div class="card mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark text-primary me-2"></i>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${file.filename}</h6>
                                    <small class="text-muted">
                                        大小: ${(file.size / 1024).toFixed(1)} KB |
                                        类型: ${file.type} |
                                        状态: ${file.status}
                                    </small>
                                </div>
                                <span class="badge bg-success">✓</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    // 处理单文件上传结果 (document_info对象)
    else if (results.document_info) {
        const doc = results.document_info;
        resultsHTML = `
            <div class="alert alert-success mb-3">
                <i class="bi bi-check-circle"></i>
                ${results.message || '文档上传成功'}
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-file-earmark-text text-primary me-2" style="font-size: 1.5rem;"></i>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${doc.filename}</h6>
                            <small class="text-muted">文档ID: ${doc.document_id}</small>
                        </div>
                    </div>
                    <div class="row g-2">
                        <div class="col-6">
                            <small class="text-muted">文件大小:</small><br>
                            <span>${(doc.size / 1024).toFixed(1)} KB</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">文件类型:</small><br>
                            <span>${doc.extension}</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">处理状态:</small><br>
                            <span class="badge bg-warning">${doc.processing_status}</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">预计完成:</small><br>
                            <span>${doc.estimated_completion}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    // 处理图像上传结果
    else if (results.image_info) {
        const img = results.image_info;
        resultsHTML = `
            <div class="alert alert-success mb-3">
                <i class="bi bi-check-circle"></i>
                ${results.message || '图像上传成功'}
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-image text-success me-2" style="font-size: 1.5rem;"></i>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${img.filename}</h6>
                            <small class="text-muted">图像ID: ${img.image_id}</small>
                        </div>
                    </div>
                    <div class="row g-2">
                        <div class="col-6">
                            <small class="text-muted">尺寸:</small><br>
                            <span>${img.processing_results.dimensions}</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">色彩空间:</small><br>
                            <span>${img.processing_results.color_space}</span>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">检测特征:</small><br>
                            <span>${img.processing_results.features_detected.join(', ')}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    // 处理OCR结果
    else if (results.ocr_info) {
        const ocr = results.ocr_info;
        resultsHTML = `
            <div class="alert alert-success mb-3">
                <i class="bi bi-check-circle"></i>
                ${results.message || 'OCR识别成功'}
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-eye text-info me-2" style="font-size: 1.5rem;"></i>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${ocr.filename}</h6>
                            <small class="text-muted">OCR ID: ${ocr.ocr_id}</small>
                        </div>
                        <span class="badge bg-success">${(ocr.ocr_results.confidence * 100).toFixed(1)}%</span>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">识别内容:</small>
                        <div class="border rounded p-2 bg-light">
                            <pre class="mb-0" style="white-space: pre-wrap;">${ocr.ocr_results.text_content}</pre>
                        </div>
                    </div>
                    <div class="row g-2">
                        <div class="col-6">
                            <small class="text-muted">字数:</small><br>
                            <span>${ocr.ocr_results.word_count}</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">处理时间:</small><br>
                            <span>${ocr.ocr_results.processing_time}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    // 处理缺陷检测结果
    else if (results.defect_info) {
        const defect = results.defect_info;
        resultsHTML = `
            <div class="alert alert-success mb-3">
                <i class="bi bi-check-circle"></i>
                ${results.message || '缺陷检测完成'}
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-bug text-warning me-2" style="font-size: 1.5rem;"></i>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${defect.filename}</h6>
                            <small class="text-muted">检测ID: ${defect.detection_id}</small>
                        </div>
                        <span class="badge bg-danger">${defect.detection_results.defects_found} 个缺陷</span>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">发现的缺陷:</small>
                        <div class="mt-2">
                            ${defect.detection_results.defect_types.map((type, index) => `
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="badge bg-secondary">${type}</span>
                                    <span class="text-muted">${defect.detection_results.severity_levels[index]}</span>
                                    <span class="badge bg-info">${(defect.detection_results.confidence_scores[index] * 100).toFixed(1)}%</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">处理建议:</small>
                        <ul class="list-unstyled mt-2">
                            ${defect.detection_results.recommendations.map(rec => `
                                <li><i class="bi bi-arrow-right text-primary"></i> ${rec}</li>
                            `).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
    // 处理波形分析结果
    else if (results.waveform_info) {
        const wave = results.waveform_info;
        resultsHTML = `
            <div class="alert alert-success mb-3">
                <i class="bi bi-check-circle"></i>
                ${results.message || '波形分析完成'}
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-graph-up text-primary me-2" style="font-size: 1.5rem;"></i>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${wave.filename}</h6>
                            <small class="text-muted">分析ID: ${wave.analysis_id}</small>
                        </div>
                        <span class="badge bg-success">质量: ${(wave.analysis_results.quality_score * 100).toFixed(1)}%</span>
                    </div>
                    <div class="row g-2 mb-3">
                        <div class="col-6">
                            <small class="text-muted">采样率:</small><br>
                            <span>${wave.analysis_results.sample_rate}</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">持续时间:</small><br>
                            <span>${wave.analysis_results.duration}</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">幅值范围:</small><br>
                            <span>${wave.analysis_results.amplitude_range}</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">异常数量:</small><br>
                            <span class="badge bg-warning">${wave.analysis_results.anomalies_detected}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">频率成分:</small><br>
                        <span>${wave.analysis_results.frequency_components.join(', ')}</span>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">异常类型:</small><br>
                        ${wave.analysis_results.anomaly_types.map(type => `
                            <span class="badge bg-warning me-1">${type}</span>
                        `).join('')}
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">分析建议:</small>
                        <ul class="list-unstyled mt-2">
                            ${wave.analysis_results.recommendations.map(rec => `
                                <li><i class="bi bi-arrow-right text-primary"></i> ${rec}</li>
                            `).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
    // 处理其他成功响应
    else if (results.message) {
        resultsHTML = `
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i>
                ${results.message}
            </div>
        `;
    }
    // 处理旧格式兼容性
    else if (results.results && Array.isArray(results.results)) {
        resultsHTML = results.results.map(result => `
            <div class="result-item">
                <div class="result-item-header">
                    <i class="bi bi-file-earmark"></i>
                    ${result.filename || '未知文件'}
                </div>
                <div class="result-item-content">
                    ${result.content || result.text || result.analysis || '处理完成'}
                </div>
                ${result.confidence ? `<small class="text-muted">置信度: ${(result.confidence * 100).toFixed(1)}%</small>` : ''}
            </div>
        `).join('');
    }
    else {
        resultsHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i>
                未知的响应格式
            </div>
        `;
    }

    resultsContainer.innerHTML = resultsHTML;
}

// 显示上传错误
function displayUploadError(error) {
    const resultsContainer = document.getElementById('upload-results');

    const errorHTML = `
        <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>上传失败</strong>
            <p class="mb-0 mt-2">${error.message || '未知错误'}</p>
        </div>
        <div class="card border-danger">
            <div class="card-body">
                <h6 class="text-danger">
                    <i class="bi bi-bug"></i> 错误详情
                </h6>
                <div class="small text-muted">
                    <p><strong>错误类型:</strong> ${error.name || 'Error'}</p>
                    <p><strong>错误信息:</strong> ${error.message || '未知错误'}</p>
                    <p><strong>发生时间:</strong> ${new Date().toLocaleString()}</p>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="uploadTester.clearResults()">
                        <i class="bi bi-arrow-clockwise"></i> 重新尝试
                    </button>
                </div>
            </div>
        </div>
    `;

    resultsContainer.innerHTML = errorHTML;
}

// 工具函数
function getEquipmentTypeText(type) {
    const typeMap = {
        'transformer': '变压器',
        'breaker': '断路器',
        'switch': '隔离开关',
        'capacitor': '电容器',
        'arrester': '避雷器',
        'cable': '电缆',
        'busbar': '母线'
    };
    return typeMap[type] || type;
}

function getStatusText(status) {
    const statusMap = {
        'running': '运行',
        'standby': '备用',
        'maintenance': '检修',
        'fault': '故障',
        'offline': '离线',
        'unknown': '未知',
        'normal': '正常',
        'warning': '警告',
        'error': '错误',
        'stopped': '停机'
    };
    return statusMap[status] || status;
}

// 获取状态CSS类
function getStatusClass(status) {
    const statusClassMap = {
        'running': 'status-running',
        'normal': 'status-running',
        'standby': 'status-standby',
        'maintenance': 'status-maintenance',
        'fault': 'status-fault',
        'error': 'status-fault',
        'offline': 'status-offline',
        'warning': 'status-warning',
        'unknown': 'status-unknown',
        'stopped': 'status-stopped'
    };
    return statusClassMap[status] || 'status-unknown';
}

// 获取状态图标
function getStatusIcon(status) {
    const statusIconMap = {
        'running': 'bi-play-circle-fill',
        'normal': 'bi-check-circle-fill',
        'standby': 'bi-pause-circle-fill',
        'maintenance': 'bi-tools',
        'fault': 'bi-exclamation-triangle-fill',
        'error': 'bi-x-circle-fill',
        'offline': 'bi-power',
        'warning': 'bi-exclamation-circle-fill',
        'unknown': 'bi-question-circle-fill',
        'stopped': 'bi-stop-circle-fill'
    };
    return statusIconMap[status] || 'bi-question-circle';
}

// 获取健康状态文本
function getHealthText(health) {
    const healthMap = {
        'good': '良好',
        'fair': '一般',
        'poor': '较差',
        'critical': '严重',
        'unknown': '未知'
    };
    return healthMap[health] || health;
}

// 获取健康状态CSS类
function getHealthClass(health) {
    const healthClassMap = {
        'good': 'health-good',
        'fair': 'health-fair',
        'poor': 'health-poor',
        'critical': 'health-critical',
        'unknown': 'health-unknown'
    };
    return healthClassMap[health] || 'health-unknown';
}

// 获取健康状态图标
function getHealthIcon(health) {
    const healthIconMap = {
        'good': 'bi-heart-fill',
        'fair': 'bi-heart-half',
        'poor': 'bi-heart',
        'critical': 'bi-heartbreak-fill',
        'unknown': 'bi-question-circle'
    };
    return healthIconMap[health] || 'bi-question-circle';
}

// 获取设备类型图标
function getEquipmentIcon(type) {
    const iconMap = {
        'transformer': 'bi-lightning-charge',
        'breaker': 'bi-toggle-on',
        'circuit_breaker': 'bi-toggle-on',
        'switch': 'bi-toggle2-on',
        'disconnector': 'bi-toggle2-off',
        'capacitor': 'bi-battery',
        'arrester': 'bi-shield-check',
        'cable': 'bi-bezier2',
        'busbar': 'bi-diagram-3',
        'reactor': 'bi-cpu',
        'generator': 'bi-gear-wide-connected',
        'motor': 'bi-gear-fill',
        'protection_device': 'bi-shield-fill-check',
        'measurement_device': 'bi-speedometer2'
    };
    return iconMap[type] || 'bi-gear';
}

// 获取状态颜色
function getStatusColor(status) {
    const colorMap = {
        '运行': 'success',
        '正常': 'success',
        '维护': 'warning',
        '检修': 'warning',
        '故障': 'danger',
        '停运': 'secondary',
        '离线': 'secondary'
    };
    return colorMap[status] || 'secondary';
}

// 查看设备详情
async function viewEquipmentDetail(equipmentId) {
    try {
        const response = await fetch(`${API_BASE_URL}/equipment/${equipmentId}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        if (data.success && data.equipment) {
            showEquipmentDetailModal(data.equipment, data.history || []);
        } else {
            showAlert(data.error || '获取设备详情失败', 'danger');
        }
    } catch (error) {
        console.error('获取设备详情失败:', error);
        showAlert('获取设备详情失败', 'danger');
    }
}

// 显示设备详情模态框
function showEquipmentDetailModal(equipment, history) {
    const modalHtml = `
        <div class="modal fade" id="equipmentDetailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi ${getEquipmentIcon(equipment.type)}"></i>
                            ${equipment.name || '设备详情'}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-info-circle"></i> 基本信息</h6>
                                <table class="table table-sm">
                                    <tr><td>设备ID:</td><td>${equipment.id || equipment.equipment_id}</td></tr>
                                    <tr><td>设备名称:</td><td>${equipment.name}</td></tr>
                                    <tr><td>设备类型:</td><td>${getEquipmentTypeText(equipment.type)}</td></tr>
                                    <tr><td>制造商:</td><td>${equipment.manufacturer || '未知'}</td></tr>
                                    <tr><td>型号:</td><td>${equipment.model || '未知'}</td></tr>
                                    <tr><td>序列号:</td><td>${equipment.serial_number || '未知'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-geo-alt"></i> 位置信息</h6>
                                <table class="table table-sm">
                                    <tr><td>安装位置:</td><td>${equipment.location || '未指定'}</td></tr>
                                    <tr><td>变电站:</td><td>${equipment.substation || '未指定'}</td></tr>
                                    <tr><td>间隔:</td><td>${equipment.bay || '未指定'}</td></tr>
                                    <tr><td>电压等级:</td><td>${equipment.voltage_level || '未指定'}</td></tr>
                                </table>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6><i class="bi bi-activity"></i> 状态信息</h6>
                                <div class="status-display">
                                    <span class="status-badge ${getStatusClass(equipment.status)}">
                                        <i class="bi ${getStatusIcon(equipment.status)}"></i>
                                        ${getStatusText(equipment.status)}
                                    </span>
                                    ${equipment.health ? `
                                    <br><span class="health-badge ${getHealthClass(equipment.health)}">
                                        <i class="bi ${getHealthIcon(equipment.health)}"></i>
                                        ${getHealthText(equipment.health)}
                                    </span>` : ''}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-speedometer2"></i> 运行参数</h6>
                                <table class="table table-sm">
                                    ${equipment.voltage ? `<tr><td>电压:</td><td>${equipment.voltage} V</td></tr>` : ''}
                                    ${equipment.current ? `<tr><td>电流:</td><td>${equipment.current} A</td></tr>` : ''}
                                    ${equipment.power ? `<tr><td>功率:</td><td>${equipment.power} W</td></tr>` : ''}
                                    ${equipment.temperature ? `<tr><td>温度:</td><td>${equipment.temperature} °C</td></tr>` : ''}
                                </table>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6><i class="bi bi-tools"></i> 维护信息</h6>
                                <table class="table table-sm">
                                    <tr><td>安装日期:</td><td>${formatDate(equipment.installation_date) || '未知'}</td></tr>
                                    <tr><td>上次维护:</td><td>${formatDate(equipment.last_maintenance) || '未知'}</td></tr>
                                    <tr><td>下次维护:</td><td>${formatDate(equipment.next_maintenance) || '未安排'}</td></tr>
                                    <tr><td>保修到期:</td><td>${formatDate(equipment.warranty_expiry) || '未知'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-gear"></i> 技术参数</h6>
                                <table class="table table-sm">
                                    ${equipment.rated_voltage ? `<tr><td>额定电压:</td><td>${equipment.rated_voltage} V</td></tr>` : ''}
                                    ${equipment.rated_current ? `<tr><td>额定电流:</td><td>${equipment.rated_current} A</td></tr>` : ''}
                                    ${equipment.rated_power ? `<tr><td>额定功率:</td><td>${equipment.rated_power} W</td></tr>` : ''}
                                    ${equipment.frequency ? `<tr><td>频率:</td><td>${equipment.frequency} Hz</td></tr>` : ''}
                                </table>
                            </div>
                        </div>

                        ${history && history.length > 0 ? `
                        <div class="mt-3">
                            <h6><i class="bi bi-clock-history"></i> 状态历史</h6>
                            <div class="table-responsive" style="max-height: 200px; overflow-y: auto;">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>旧状态</th>
                                            <th>新状态</th>
                                            <th>备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${history.map(record => `
                                            <tr>
                                                <td>${formatDateTime(record.timestamp)}</td>
                                                <td><span class="status-badge ${getStatusClass(record.old_status)}">${getStatusText(record.old_status)}</span></td>
                                                <td><span class="status-badge ${getStatusClass(record.new_status)}">${getStatusText(record.new_status)}</span></td>
                                                <td>${record.notes || '-'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="editEquipment('${equipment.id || equipment.equipment_id}')">编辑设备</button>
                        <button type="button" class="btn btn-warning" onclick="updateEquipmentStatus('${equipment.id || equipment.equipment_id}')">更新状态</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('equipmentDetailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new window.bootstrap.Modal(document.getElementById('equipmentDetailModal'));
    modal.show();
}

// 更新设备状态
async function updateEquipmentStatus(equipmentId) {
    const statusOptions = [
        { value: 'running', text: '运行', class: 'status-running' },
        { value: 'standby', text: '备用', class: 'status-standby' },
        { value: 'maintenance', text: '检修', class: 'status-maintenance' },
        { value: 'fault', text: '故障', class: 'status-fault' },
        { value: 'offline', text: '离线', class: 'status-offline' }
    ];

    const modalHtml = `
        <div class="modal fade" id="updateStatusModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-arrow-repeat"></i> 更新设备状态
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="updateStatusForm">
                            <div class="mb-3">
                                <label for="newStatus" class="form-label">新状态</label>
                                <select class="form-select" id="newStatus" required>
                                    <option value="">请选择状态...</option>
                                    ${statusOptions.map(option => `
                                        <option value="${option.value}" class="${option.class}">
                                            ${option.text}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="statusNotes" class="form-label">备注</label>
                                <textarea class="form-control" id="statusNotes" rows="3"
                                    placeholder="请输入状态变更的原因或备注信息..."></textarea>
                            </div>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                状态更新后将记录到设备历史中，并可能触发相关通知。
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitStatusUpdate('${equipmentId}')">
                            <i class="bi bi-check"></i> 确认更新
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('updateStatusModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new window.bootstrap.Modal(document.getElementById('updateStatusModal'));
    modal.show();
}

// 提交状态更新
async function submitStatusUpdate(equipmentId) {
    const newStatus = document.getElementById('newStatus').value;
    const notes = document.getElementById('statusNotes').value;

    if (!newStatus) {
        showAlert('请选择新状态', 'warning');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/equipment/${equipmentId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: newStatus,
                notes: notes
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            showAlert('设备状态更新成功', 'success');

            // 关闭模态框
            const modal = window.bootstrap.Modal.getInstance(document.getElementById('updateStatusModal'));
            if (modal) {
                modal.hide();
            }

            // 优化：只在当前页面是设备管理页面时才重新加载
            const currentTab = document.querySelector('.main-tab-content.active');
            if (currentTab && currentTab.id === 'equipment-management') {
                loadEquipmentList();
            } else {
                // 标记数据过期，下次切换到设备管理页面时会重新加载
                window.equipmentDataTimestamp = 0;
            }

        } else {
            showAlert(data.error || '状态更新失败', 'danger');
        }

    } catch (error) {
        console.error('更新设备状态失败:', error);
        showAlert('更新设备状态失败', 'danger');
    }
}

// 更新设备统计信息 - 现代化版本
function updateEquipmentStatistics(statistics) {
    // 更新设备统计卡片
    const totalElement = document.getElementById('total-equipment');
    const runningElement = document.getElementById('running-equipment');
    const maintenanceElement = document.getElementById('maintenance-equipment');
    const faultElement = document.getElementById('fault-equipment');

    if (!totalElement || !runningElement || !maintenanceElement || !faultElement) {

        return;
    }

    if (!statistics || !statistics.status) {
        // 设置默认值
        totalElement.textContent = '0';
        runningElement.textContent = '0';
        maintenanceElement.textContent = '0';
        faultElement.textContent = '0';
        return;
    }

    const statusStats = statistics.status || {};

    // 计算总数
    const totalEquipment = Object.values(statusStats).reduce((sum, count) => sum + count, 0);
    const runningCount = statusStats.running || 0;
    const maintenanceCount = statusStats.maintenance || 0;
    const faultCount = statusStats.fault || 0;

    // 更新统计数字
    totalElement.textContent = totalEquipment;
    runningElement.textContent = runningCount;
    maintenanceElement.textContent = maintenanceCount;
    faultElement.textContent = faultCount;

}

// 更新设备数量徽章
function updateEquipmentCountBadge(count) {
    const badge = document.getElementById('equipment-count-badge');
    if (badge) {
        badge.textContent = `${count} 台设备`;
        badge.style.animation = 'pulse 0.5s ease-in-out';
        setTimeout(() => {
            badge.style.animation = '';
        }, 500);
    }
}

// 图表功能已集成到现代化统计卡片中

// 添加搜索功能
function initEquipmentSearch() {
    const searchInput = document.getElementById('equipment-search');
    if (!searchInput) return;

    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            const searchValue = this.value.trim();
            loadEquipmentList({ search: searchValue });
        }, 500); // 500ms 防抖
    });

    // 添加搜索按钮点击事件
    const searchButton = searchInput.nextElementSibling;
    if (searchButton) {
        searchButton.addEventListener('click', function() {
            const searchValue = searchInput.value.trim();
            loadEquipmentList({ search: searchValue });
        });
    }
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    } catch (error) {
        return dateString;
    }
}

// 清除筛选器
function clearFilters() {
    document.getElementById('equipment-search').value = '';
    document.getElementById('type-filter').value = '';
    document.getElementById('status-filter').value = '';
    document.getElementById('location-filter').value = '';
    loadEquipmentList();
}

// 初始化筛选器事件监听
function initEquipmentFilters() {
    const typeFilter = document.getElementById('filter-type');
    const statusFilter = document.getElementById('filter-status');
    const locationFilter = document.getElementById('filter-location');

    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            applyEquipmentFilters();
        });
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            applyEquipmentFilters();
        });
    }

    if (locationFilter) {
        let filterTimeout;
        locationFilter.addEventListener('input', function() {
            clearTimeout(filterTimeout);
            filterTimeout = setTimeout(() => {
                applyFilters();
            }, 500);
        });
    }
}

// 应用筛选器
function applyFilters() {
    const search = document.getElementById('equipment-search')?.value || '';
    const type = document.getElementById('type-filter')?.value || '';
    const status = document.getElementById('status-filter')?.value || '';
    const location = document.getElementById('location-filter')?.value || '';

    const filters = {};
    if (search) filters.search = search;
    if (type) filters.type = type;
    if (status) filters.status = status;
    if (location) filters.location = location;

    loadEquipmentList(filters);
}

// 应用设备筛选
function applyEquipmentFilters() {
    const typeFilter = document.getElementById('filter-type');
    const statusFilter = document.getElementById('filter-status');
    const locationFilter = document.getElementById('filter-location');

    const filters = {};

    if (typeFilter && typeFilter.value) {
        filters.type = typeFilter.value;
    }

    if (statusFilter && statusFilter.value) {
        filters.status = statusFilter.value;
    }

    if (locationFilter && locationFilter.value.trim()) {
        filters.location = locationFilter.value.trim();
    }

    loadEquipmentList(filters);
}

// 清除设备筛选
function clearEquipmentFilters() {
    const typeFilter = document.getElementById('filter-type');
    const statusFilter = document.getElementById('filter-status');
    const locationFilter = document.getElementById('filter-location');
    const searchInput = document.getElementById('equipment-search');

    if (typeFilter) typeFilter.value = '';
    if (statusFilter) statusFilter.value = '';
    if (locationFilter) locationFilter.value = '';
    if (searchInput) searchInput.value = '';

    loadEquipmentList();
}

// 更新搜索结果统计
function updateEquipmentSearchStats(filteredCount, totalCount) {
    const statsElement = document.getElementById('equipment-search-stats');
    if (!statsElement) return;

    if (filteredCount === totalCount) {
        statsElement.textContent = `共 ${totalCount} 台设备`;
    } else {
        statsElement.textContent = `显示 ${filteredCount} / ${totalCount} 台设备`;
    }
}

// 编辑设备
async function editEquipment(equipmentId) {
    try {
        const response = await fetch(`${API_BASE_URL}/equipment/${equipmentId}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            populateEquipmentForm(data.equipment);
            showAlert('设备信息已加载到表单，请修改后提交', 'info');
        } else {
            showAlert(data.error || '获取设备信息失败', 'danger');
        }

    } catch (error) {
        console.error('获取设备信息失败:', error);
        showAlert('获取设备信息失败', 'danger');
    }
}

// 删除设备
async function deleteEquipment(equipmentId) {
    if (!confirm('确定要删除这台设备吗？此操作不可撤销。')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/equipment/${equipmentId}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            showAlert('设备删除成功', 'success');

            // 优化：只在当前页面是设备管理页面时才重新加载
            const currentTab = document.querySelector('.main-tab-content.active');
            if (currentTab && currentTab.id === 'equipment-management') {
                loadEquipmentList();
            } else {
                // 标记数据过期，下次切换到设备管理页面时会重新加载
                window.equipmentDataTimestamp = 0;
            }
        } else {
            showAlert(data.error || '删除设备失败', 'danger');
        }

    } catch (error) {
        console.error('删除设备失败:', error);
        showAlert('删除设备失败', 'danger');
    }
}

// 初始化设备管理页面
function initEquipmentManagement() {
    // 初始化搜索功能
    initEquipmentSearch();

    // 初始化筛选器
    initEquipmentFilters();

    // 加载设备列表
    loadEquipmentList();

    // 设备表单已在DOMContentLoaded中初始化，无需重复绑定
}

// 显示设备详情模态框
function showEquipmentDetailModal(equipment) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="equipmentDetailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">设备详情 - ${equipment.name || equipment.id}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基本信息</h6>
                                <table class="table table-sm">
                                    <tr><td>设备ID:</td><td>${equipment.id || equipment.equipment_id || '未知'}</td></tr>
                                    <tr><td>设备名称:</td><td>${equipment.name || '未知'}</td></tr>
                                    <tr><td>设备类型:</td><td>${equipment.type || '未知'}</td></tr>
                                    <tr><td>制造商:</td><td>${equipment.manufacturer || '未知'}</td></tr>
                                    <tr><td>型号:</td><td>${equipment.model || '未知'}</td></tr>
                                    <tr><td>序列号:</td><td>${equipment.serial_number || '未知'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>状态信息</h6>
                                <table class="table table-sm">
                                    <tr><td>运行状态:</td><td><span class="badge bg-${getStatusColor(equipment.status)}">${equipment.status || '未知'}</span></td></tr>
                                    <tr><td>健康状态:</td><td>${equipment.health || '未知'}</td></tr>
                                    <tr><td>位置:</td><td>${equipment.location || '未知'}</td></tr>
                                    <tr><td>电压等级:</td><td>${equipment.voltage_level || '未知'}</td></tr>
                                    <tr><td>最后更新:</td><td>${formatDateTime(equipment.updated_at)}</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="editEquipment('${equipment.id || equipment.equipment_id}')" data-bs-dismiss="modal">编辑设备</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('equipmentDetailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new window.bootstrap.Modal(document.getElementById('equipmentDetailModal'));
    modal.show();
}

// 填充设备表单
function populateEquipmentForm(equipment) {
    document.getElementById('equipment-name').value = equipment.name || '';
    document.getElementById('equipment-type').value = equipment.type || '';
    document.getElementById('equipment-location').value = equipment.location || '';
    document.getElementById('equipment-status').value = equipment.status || '';

    // 修改表单提交行为为更新模式
    const form = document.getElementById('equipment-form');
    const submitBtn = form.querySelector('button[type="submit"]');

    // 移除添加设备的事件监听器，添加更新设备的事件监听器
    form.removeEventListener('submit', handleAddEquipment);

    // 创建更新设备的处理函数
    const handleUpdateEquipmentWrapper = function(e) {
        e.preventDefault();
        updateEquipment(equipment.id || equipment.equipment_id);
    };

    // 保存更新处理器的引用，以便后续移除
    form.updateHandler = handleUpdateEquipmentWrapper;
    form.addEventListener('submit', handleUpdateEquipmentWrapper);

    submitBtn.innerHTML = '<i class="bi bi-check"></i> 更新设备';
    submitBtn.className = 'btn btn-warning';

    // 添加取消按钮
    if (!form.querySelector('.cancel-edit-btn')) {
        const cancelBtn = document.createElement('button');
        cancelBtn.type = 'button';
        cancelBtn.className = 'btn btn-secondary ms-2 cancel-edit-btn';
        cancelBtn.innerHTML = '<i class="bi bi-x"></i> 取消';
        cancelBtn.onclick = resetEquipmentForm;
        submitBtn.parentNode.insertBefore(cancelBtn, submitBtn.nextSibling);
    }
}

// 更新设备
async function updateEquipment(equipmentId) {

    const formData = {
        name: document.getElementById('equipment-name').value,
        type: document.getElementById('equipment-type').value,
        location: document.getElementById('equipment-location').value,
        status: document.getElementById('equipment-status').value
    };

    try {

        const response = await fetch(`${API_BASE_URL}/equipment/${equipmentId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            console.error('❌ HTTP错误:', response.status, response.statusText);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {

            showAlert('设备更新成功', 'success');
            resetEquipmentForm();

            // 优化：只在当前页面是设备管理页面时才重新加载
            const currentTab = document.querySelector('.main-tab-content.active');
            if (currentTab && currentTab.id === 'equipment-management') {
                loadEquipmentList();
            } else {
                // 标记数据过期，下次切换到设备管理页面时会重新加载
                window.equipmentDataTimestamp = 0;
            }
        } else {
            console.error('❌ 服务器返回错误:', data.error);
            showAlert(data.error || '更新设备失败', 'danger');
        }

    } catch (error) {
        console.error('❌ 更新设备失败:', error);
        console.error('❌ 错误详情:', error.message);
        showAlert('更新设备失败: ' + error.message, 'danger');
    }
}

// 重置设备表单
function resetEquipmentForm() {
    const form = document.getElementById('equipment-form');
    const submitBtn = form.querySelector('button[type="submit"]');
    const cancelBtn = form.querySelector('.cancel-edit-btn');

    // 重置表单
    form.reset();

    // 移除更新设备的事件监听器并重新绑定添加设备处理器
    if (form.updateHandler) {
        form.removeEventListener('submit', form.updateHandler);
        form.updateHandler = null;
    }
    form.removeEventListener('submit', handleAddEquipment);
    form.addEventListener('submit', handleAddEquipment);

    // 恢复按钮样式
    submitBtn.innerHTML = '<i class="bi bi-plus"></i> 添加设备';
    submitBtn.className = 'btn btn-primary';

    // 移除取消按钮
    if (cancelBtn) {
        cancelBtn.remove();
    }
}

function formatDateTime(dateString) {
    if (!dateString) return '未知';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 加载设备历史记录
async function loadEquipmentHistory(equipmentId) {
    try {
        const response = await fetch(`${API_BASE_URL}/equipment/${equipmentId}/history`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const history = data.history || data || [];
        displayEquipmentHistory(history);

    } catch (error) {
        console.error('加载设备历史失败:', error);
        showAlert('加载设备历史失败', 'danger');
    }
}

// 显示设备历史记录
function displayEquipmentHistory(history) {
    const historyContainer = document.getElementById('equipment-history-container');
    if (!historyContainer) return;

    if (!history || history.length === 0) {
        historyContainer.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-clock-history" style="font-size: 3rem;"></i>
                <p class="mt-2">暂无历史记录</p>
            </div>
        `;
        return;
    }

    const historyHTML = history.map(record => `
        <div class="history-item">
            <div class="history-time">${formatDateTime(record.timestamp)}</div>
            <div class="history-action">${record.action || '未知操作'}</div>
            <div class="history-details">${record.details || '无详细信息'}</div>
            <div class="history-user">操作人: ${record.user || '系统'}</div>
        </div>
    `).join('');

    historyContainer.innerHTML = historyHTML;
}

// 加载设备绑定关系
async function loadEquipmentBindings(equipmentId) {
    try {
        const response = await fetch(`${API_BASE_URL}/equipment/${equipmentId}/bindings`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const bindings = data.bindings || data || [];
        displayEquipmentBindings(bindings);

    } catch (error) {
        console.error('加载设备绑定失败:', error);
        showAlert('加载设备绑定失败', 'danger');
    }
}

// 显示设备绑定关系
function displayEquipmentBindings(bindings) {
    const bindingsContainer = document.getElementById('equipment-bindings-container');
    if (!bindingsContainer) return;

    if (!bindings || bindings.length === 0) {
        bindingsContainer.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-link" style="font-size: 3rem;"></i>
                <p class="mt-2">暂无绑定关系</p>
            </div>
        `;
        return;
    }

    const bindingsHTML = bindings.map(binding => `
        <div class="binding-item">
            <div class="binding-type">${binding.type || '未知类型'}</div>
            <div class="binding-target">${binding.target_name || '未知目标'}</div>
            <div class="binding-status">
                <span class="badge ${binding.status === 'active' ? 'bg-success' : 'bg-secondary'}">
                    ${binding.status === 'active' ? '活跃' : '非活跃'}
                </span>
            </div>
            <div class="binding-created">创建时间: ${formatDateTime(binding.created_at)}</div>
        </div>
    `).join('');

    bindingsContainer.innerHTML = bindingsHTML;
}

// 显示警告消息
function showAlert(message, type = 'info') {
    // 创建警告元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 添加到页面顶部
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // 自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 显示增强通知
function showEnhancedNotification(title, message, type = 'info', duration = 5000) {
    // 创建通知容器（如果不存在）
    let notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        notificationContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 350px;
        `;
        document.body.appendChild(notificationContainer);
    }

    // 创建通知元素
    const notificationDiv = document.createElement('div');
    notificationDiv.className = `alert alert-${type} alert-dismissible fade show mb-2`;
    notificationDiv.style.cssText = `
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    `;

    const iconMap = {
        'success': 'bi-check-circle',
        'danger': 'bi-exclamation-triangle',
        'warning': 'bi-exclamation-circle',
        'info': 'bi-info-circle'
    };

    const icon = iconMap[type] || iconMap['info'];

    notificationDiv.innerHTML = `
        <div class="d-flex align-items-start">
            <i class="bi ${icon} me-2 mt-1"></i>
            <div class="flex-grow-1">
                <strong>${title}</strong><br>
                <small>${message}</small>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 添加到容器
    notificationContainer.appendChild(notificationDiv);

    // 自动移除
    if (duration > 0) {
        setTimeout(() => {
            if (notificationDiv.parentNode) {
                notificationDiv.remove();
            }
        }, duration);
    }

    return notificationDiv;
}

// 更新数据指示器
function updateDataIndicators() {

    // 更新设备数量指示器
    const equipmentCountEl = document.getElementById('equipment-count');
    if (equipmentCountEl && equipmentList) {
        equipmentCountEl.textContent = equipmentList.length;
    }

    // 更新知识库文档数量
    const knowledgeCountEl = document.getElementById('knowledge-count');
    if (knowledgeCountEl) {
        // 这里可以从API获取实际数量
        knowledgeCountEl.textContent = '100+';
    }

    // 更新分析历史数量
    const historyCountEl = document.getElementById('history-count');
    if (historyCountEl && analysisHistory) {
        historyCountEl.textContent = analysisHistory.length;
    }
}

// 更新状态指示器
function updateStatusIndicators(status = null) {

    // 系统状态指示器
    const systemStatusEl = safeGetElement('system-status', true);
    if (systemStatusEl) {
        if (status && status.system_healthy) {
            systemStatusEl.innerHTML = '<i class="bi bi-check-circle text-success"></i> 系统正常';
            systemStatusEl.className = 'badge bg-success';
        } else {
            systemStatusEl.innerHTML = '<i class="bi bi-exclamation-triangle text-warning"></i> 检查中';
            systemStatusEl.className = 'badge bg-warning';
        }
    }

    // API状态指示器
    const apiStatusEl = document.getElementById('api-status');
    if (apiStatusEl) {
        if (status && status.api_available) {
            apiStatusEl.innerHTML = '<i class="bi bi-wifi text-success"></i> API连接正常';
            apiStatusEl.className = 'badge bg-success';
        } else {
            apiStatusEl.innerHTML = '<i class="bi bi-wifi-off text-danger"></i> API连接异常';
            apiStatusEl.className = 'badge bg-danger';
        }
    }

    // 知识库状态指示器
    const kbStatusEl = document.getElementById('kb-status');
    if (kbStatusEl) {
        if (status && status.knowledge_base_ready) {
            kbStatusEl.innerHTML = '<i class="bi bi-database text-success"></i> 知识库就绪';
            kbStatusEl.className = 'badge bg-success';
        } else {
            kbStatusEl.innerHTML = '<i class="bi bi-database-x text-warning"></i> 知识库初始化中';
            kbStatusEl.className = 'badge bg-warning';
        }
    }
}

// 导出分析结果
function exportAnalysisResult() {
    const faultCauses = document.getElementById('fault-causes').textContent;
    const faultLogic = document.getElementById('fault-logic').textContent;
    const recommendations = document.getElementById('recommendations').textContent;

    const content = `故障分析报告
生成时间: ${new Date().toLocaleString()}

可能故障原因:
${faultCauses}

故障逻辑链条:
${faultLogic}

处理建议:
${recommendations}
`;

    // 创建下载链接
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `故障分析报告_${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 保存分析结果
async function saveAnalysisResult() {
    const faultCauses = document.getElementById('fault-causes').textContent;
    const faultLogic = document.getElementById('fault-logic').textContent;
    const recommendations = document.getElementById('recommendations').textContent;

    const data = {
        fault_causes: faultCauses,
        fault_logic: faultLogic,
        recommendations: recommendations,
        timestamp: new Date().toISOString()
    };

    try {
        const response = await fetch(`${API_BASE_URL}/fault/save`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            showAlert('分析结果已保存', 'success');
        } else {
            throw new Error('保存失败');
        }
    } catch (error) {
        console.error('保存分析结果失败:', error);
        showAlert('保存失败，请稍后重试', 'danger');
    }
}

// 显示添加文档模态框
function showAddDocumentModal() {

    const modalElement = document.getElementById('addDocumentModal');
    if (!modalElement) {
        console.error('❌ 找不到addDocumentModal元素');
        alert('错误：找不到文档添加模态框');
        return;
    }

    try {
        // 重置表单
        resetDocumentForm();

        // 使用自定义模态框显示方法
        showModal('addDocumentModal');

        // 初始化标签输入
        initializeDocumentTagInput();

    } catch (error) {
        console.error('❌ 显示文档模态框失败:', error);
        // 降级处理：直接显示模态框
        modalElement.style.display = 'block';
        modalElement.classList.add('show');
    }
}

// 重置文档表单
function resetDocumentForm() {
    const form = document.getElementById('add-document-form');
    if (form) form.reset();

    const fileInput = document.getElementById('doc-file');
    if (fileInput) fileInput.value = '';

    // 清空关键词显示
    const extractedKeywords = document.getElementById('extracted-keywords');
    if (extractedKeywords) extractedKeywords.innerHTML = '';

}

// 初始化文档标签输入
function initializeDocumentTagInput() {
    const tagInput = document.getElementById('doc-tags');
    if (tagInput) {
        tagInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addDocumentTag(this.value.trim());
                this.value = '';
            }
        });
    }
}

// 更新文档进度条（兼容性函数）
function updateDocProgress(percentage) {
    const progressBar = document.getElementById('doc-progress-bar');
    if (progressBar) {
        progressBar.style.width = percentage + '%';
    }
}

// 更新图片进度条（兼容性函数）
function updateImgProgress(percentage) {
    const progressBar = document.getElementById('img-progress-bar');
    if (progressBar) {
        progressBar.style.width = percentage + '%';
    }
}

// 处理文档文件选择
function handleDocFileSelect(input) {

    if (!input.files || input.files.length === 0) {

        return;
    }

    const file = input.files[0];

    // 显示文件信息
    const fileInfo = document.getElementById('docFileInfo');
    const fileName = document.getElementById('docFileName');
    const fileSize = document.getElementById('docFileSize');

    if (fileInfo && fileName && fileSize) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.classList.remove('d-none');
    }

    // 自动填充标题（如果为空）
    const titleInput = document.getElementById('doc-title');
    if (titleInput && !titleInput.value) {
        const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");
        titleInput.value = nameWithoutExt;
    }
}

// 处理图片文件选择
function handleImgFileSelect(input) {

    if (!input.files || input.files.length === 0) {

        return;
    }

    const files = Array.from(input.files);

    // 显示图片预览
    const previewContainer = document.getElementById('image-preview-container');
    const previewList = document.getElementById('image-preview-list');

    if (previewContainer && previewList) {
        previewList.innerHTML = '';

        files.forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewHtml = `
                        <div class="col-md-4 mb-2">
                            <div class="card">
                                <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">
                                <div class="card-body p-2">
                                    <small class="text-muted">${file.name}</small>
                                    <br><small class="text-muted">${formatFileSize(file.size)}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    previewList.insertAdjacentHTML('beforeend', previewHtml);
                };
                reader.readAsDataURL(file);
            }
        });

        previewContainer.classList.remove('d-none');
    }

    // 自动填充标题（如果为空）
    const titleInput = document.getElementById('img-title');
    if (titleInput && !titleInput.value && files.length > 0) {
        const nameWithoutExt = files[0].name.replace(/\.[^/.]+$/, "");
        titleInput.value = nameWithoutExt;
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 清除文档文件
function clearDocFile() {
    const fileInput = document.getElementById('doc-file');
    const fileInfo = document.getElementById('docFileInfo');

    if (fileInput) fileInput.value = '';
    if (fileInfo) fileInfo.classList.add('d-none');
}

// 显示提示信息
function showAlert(message, type = 'info') {

    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// 重置文档表单
function resetDocumentForm() {
    const form = document.getElementById('add-document-form');
    if (form) {
        form.reset();
    }

    // 清除文件信息
    clearDocFile();

    // 清除预览
    const previewContainer = document.getElementById('doc-preview-container');
    if (previewContainer) {
        previewContainer.classList.add('d-none');
    }

    // 重置标签页到第一个
    const firstTab = document.getElementById('doc-upload-tab');
    if (firstTab) {
        firstTab.click();
    }
}

// 重置图片表单
function resetImageForm() {
    const form = document.getElementById('add-image-form');
    if (form) {
        form.reset();
    }

    // 清除预览
    const previewContainer = document.getElementById('image-preview-container');
    if (previewContainer) {
        previewContainer.classList.add('d-none');
    }

    // 重置标签页到第一个
    const firstTab = document.getElementById('img-upload-tab');
    if (firstTab) {
        firstTab.click();
    }
}

// 添加文档标签
function addDocumentTag(tagText) {
    if (!tagText) return;

    const tagDisplay = document.getElementById('doc-tag-display');
    if (!tagDisplay) return;

    // 创建标签元素
    const tagElement = document.createElement('span');
    tagElement.className = 'badge bg-primary me-1 mb-1';
    tagElement.innerHTML = `
        ${tagText}
        <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;" onclick="this.parentElement.remove()"></button>
    `;

    tagDisplay.appendChild(tagElement);
}

// 初始化图片标签输入
function initializeImageTagInput() {
    const tagInput = document.getElementById('img-tags');
    if (tagInput) {
        tagInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addImageTag(this.value.trim());
                this.value = '';
            }
        });
    }
}

// 添加图片标签
function addImageTag(tagText) {
    if (!tagText) return;

    const tagDisplay = document.getElementById('img-tag-display');
    if (!tagDisplay) return;

    // 创建标签元素
    const tagElement = document.createElement('span');
    tagElement.className = 'badge bg-primary me-1 mb-1';
    tagElement.innerHTML = `
        ${tagText}
        <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;" onclick="this.parentElement.remove()"></button>
    `;

    tagDisplay.appendChild(tagElement);
}

// 处理文档文件选择
function handleDocumentFileSelect() {
    const fileInput = document.getElementById('doc-file-input');
    const fileInfo = document.getElementById('doc-file-info');
    const fileDetails = document.getElementById('doc-file-details');

    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // 显示文件信息
                fileDetails.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-file-earmark-text text-primary me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <div class="fw-bold">${file.name}</div>
                            <small class="text-muted">
                                大小: ${formatFileSize(file.size)} |
                                类型: ${file.type || '未知'} |
                                修改时间: ${new Date(file.lastModified).toLocaleString()}
                            </small>
                        </div>
                    </div>
                `;
                fileInfo.classList.remove('d-none');

                // 自动填充标题（如果为空）
                const titleInput = document.getElementById('doc-title');
                if (!titleInput.value) {
                    titleInput.value = file.name.replace(/\.[^/.]+$/, ""); // 移除扩展名
                }

                // 尝试读取文件内容
                readDocumentContent(file);
            }
        });
    }
}

// 读取文档内容
function readDocumentContent(file) {
    const contentTextarea = document.getElementById('doc-content');
    const fileExt = file.name.split('.').pop().toLowerCase();

    if (fileExt === 'txt' || fileExt === 'md') {
        const reader = new FileReader();
        reader.onload = function(e) {
            contentTextarea.value = e.target.result;
            updateDocumentPreview(e.target.result);
        };
        reader.readAsText(file);
    } else {
        contentTextarea.placeholder = `已选择 ${file.name}，内容将在服务器端提取`;
        updateDocumentPreview(`文档文件: ${file.name}\n类型: ${fileExt.toUpperCase()}\n大小: ${formatFileSize(file.size)}`);
    }
}

// 更新文档处理预览
function updateDocumentPreview(content) {
    const preview = document.getElementById('doc-processing-preview');
    if (!content) {
        preview.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-hourglass-split"></i>
                <p class="mt-2">上传文件或输入内容后将显示处理预览</p>
            </div>
        `;
        return;
    }

    // 简单的内容预览和分析
    const wordCount = content.length;
    const lineCount = content.split('\n').length;

    // 检测关键词
    const keywords = extractKeywords(content);

    // 计算质量评分
    const qualityScore = calculateDocumentQuality(content, keywords);

    preview.innerHTML = `
        <div class="small">
            <div class="mb-2">
                <strong>内容统计:</strong><br>
                字符数: ${wordCount} | 行数: ${lineCount}
            </div>
            <div class="mb-2">
                <strong>检测到的关键词:</strong><br>
                ${keywords.length > 0 ? keywords.map(k => `<span class="badge bg-secondary me-1">${k}</span>`).join('') : '无'}
            </div>
            <div class="mb-2">
                <strong>内容预览:</strong><br>
                <div class="border rounded p-2" style="max-height: 100px; overflow-y: auto; background: white;">
                    ${content.substring(0, 200)}${content.length > 200 ? '...' : ''}
                </div>
            </div>
        </div>
    `;

    // 更新质量评分
    updateDocumentQualityScores(qualityScore);
}

// 本地关键词提取辅助函数
function extractKeywordsLocal(content) {
    const keywords = [];
    const powerTerms = ['变压器', '断路器', '发电机', '输电线路', '保护装置', '故障', '缺陷', '维护', '检修', '巡检'];
    const voltagePattern = /(\d+)kV/g;

    // 检测电力术语
    powerTerms.forEach(term => {
        if (content.includes(term)) {
            keywords.push(term);
        }
    });

    // 检测电压等级
    let match;
    while ((match = voltagePattern.exec(content)) !== null) {
        keywords.push(match[0]);
    }

    return [...new Set(keywords)]; // 去重
}

// 计算文档质量
function calculateDocumentQuality(content, keywords) {
    let completeness = 0;
    let accuracy = 0;

    // 完整性评分
    if (content.length > 100) completeness += 30;
    if (content.length > 500) completeness += 30;
    if (keywords.length > 0) completeness += 40;

    // 准确性评分
    if (keywords.length > 2) accuracy += 50;
    if (content.includes('故障') || content.includes('缺陷')) accuracy += 30;
    if (/\d+kV/.test(content)) accuracy += 20;

    const overall = (completeness + accuracy) / 2;

    return { completeness, accuracy, overall };
}

// 更新文档质量评分
function updateDocumentQualityScores(scores) {
    document.getElementById('doc-completeness-score').textContent = `${Math.round(scores.completeness)}%`;
    document.getElementById('doc-accuracy-score').textContent = `${Math.round(scores.accuracy)}%`;
    document.getElementById('doc-overall-score').textContent = `${Math.round(scores.overall)}%`;
}

// 添加文档（原始版本，保持兼容性）
async function addDocument() {
    const title = document.getElementById('doc-title').value;
    const category = document.getElementById('doc-category').value;
    const content = document.getElementById('doc-content').value;
    const tags = document.getElementById('doc-tags').value;

    if (!title || !category || !content) {
        showAlert('请填写所有必填字段', 'warning');
        return;
    }

    try {
        const response = await fetch('/api/v1/knowledge/documents', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title: title,
                category: category,
                content: content,
                tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag)
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('文档添加成功！', 'success');
            closeModal('addDocumentModal');
            // 重置表单
            document.getElementById('add-document-form').reset();
            // 刷新知识库列表
            if (typeof refreshKnowledgeBase === 'function') {
                refreshKnowledgeBase();
            }
        } else {
            showAlert('添加失败: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('添加文档失败:', error);
        showAlert('添加文档失败', 'error');
    }
}

// 添加文档（增强版本，包含标注和清洗功能）
async function addDocumentWithAnnotation() {

    const title = document.getElementById('doc-title')?.value || '';
    const category = document.getElementById('doc-category')?.value || '';
    const content = document.getElementById('doc-content')?.value || '';
    const tags = document.getElementById('doc-tags')?.value || '';
    const equipment = document.getElementById('doc-equipment')?.value || '';
    const voltage = document.getElementById('doc-voltage')?.value || '';
    const faultType = document.getElementById('doc-fault-type')?.value || '';
    const fileInput = document.getElementById('doc-file');

    if (!title || !category) {
        showAlert('请填写文档标题和类别', 'warning');
        return;
    }

    try {
        const formData = new FormData();

        // 如果有文件，添加文件
        if (fileInput && fileInput.files && fileInput.files.length > 0) {
            formData.append('file', fileInput.files[0]);
        }

        // 构建元数据
        const metadata = {
            title: title,
            category: category,
            content: content,
            equipment_type: equipment,
            voltage_level: voltage,
            fault_type: faultType,
            tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
            annotation_enabled: true,
            cleaning_enabled: true
        };

        formData.append('metadata', JSON.stringify(metadata));

        const response = await fetch('/api/v1/knowledge/documents/add', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showAlert('文档添加成功！已启用智能标注和数据清洗', 'success');
            closeModal('addDocumentModal');
            // 重置表单
            resetDocumentForm();
            // 刷新知识库列表
            if (typeof refreshKnowledgeBase === 'function') {
                refreshKnowledgeBase();
            }
        } else {
            showAlert('添加失败: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('添加文档失败:', error);
        showAlert('添加文档失败', 'error');
    }
}

// 提取关键词
async function extractKeywords() {
    const content = document.getElementById('doc-content')?.value || '';
    if (!content.trim()) {
        showAlert('请先在"文档上传"标签页输入文档内容', 'warning');
        // 自动切换到文档上传标签页
        if (typeof switchDocTab === 'function') {
            switchDocTab('doc-upload');
        }
        return;
    }

    try {
        const response = await fetch('/api/v1/knowledge/extract-keywords', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ content: content })
        });

        const result = await response.json();

        if (result.success && result.keywords) {
            const keywordsContainer = document.getElementById('extracted-keywords');
            if (keywordsContainer) {
                keywordsContainer.innerHTML = '';

                result.keywords.forEach(keyword => {
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-primary me-1 mb-1';
                    badge.textContent = keyword;
                    badge.style.cursor = 'pointer';
                    badge.onclick = () => addKeywordToTags(keyword);
                    keywordsContainer.appendChild(badge);
                });

                showAlert(`成功提取 ${result.keywords.length} 个关键词`, 'success');
            }
        } else {
            // 降级处理：使用本地关键词提取
            const localKeywords = extractKeywordsLocal(content);
            const keywordsContainer = document.getElementById('extracted-keywords');
            if (keywordsContainer && localKeywords.length > 0) {
                keywordsContainer.innerHTML = '';
                localKeywords.forEach(keyword => {
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-secondary me-1 mb-1';
                    badge.textContent = keyword;
                    badge.style.cursor = 'pointer';
                    badge.onclick = () => addKeywordToTags(keyword);
                    keywordsContainer.appendChild(badge);
                });
                showAlert(`本地提取 ${localKeywords.length} 个关键词`, 'info');
            } else {
                showAlert('未找到相关关键词', 'warning');
            }
        }
    } catch (error) {
        console.error('关键词提取失败:', error);
        // 降级处理：使用本地关键词提取
        const localKeywords = extractKeywordsLocal(content);
        const keywordsContainer = document.getElementById('extracted-keywords');
        if (keywordsContainer && localKeywords.length > 0) {
            keywordsContainer.innerHTML = '';
            localKeywords.forEach(keyword => {
                const badge = document.createElement('span');
                badge.className = 'badge bg-secondary me-1 mb-1';
                badge.textContent = keyword;
                badge.style.cursor = 'pointer';
                badge.onclick = () => addKeywordToTags(keyword);
                keywordsContainer.appendChild(badge);
            });
            showAlert(`本地提取 ${localKeywords.length} 个关键词`, 'info');
        } else {
            showAlert('关键词提取失败', 'error');
        }
    }
}

// 添加技术参数
function addTechnicalParam() {
    const paramName = document.getElementById('param-name').value;
    const paramValue = document.getElementById('param-value').value;

    if (!paramName || !paramValue) {
        showAlert('请输入参数名称和值', 'warning');
        return;
    }

    const paramsContainer = document.getElementById('technical-params');

    // 如果是第一个参数，清除提示信息
    if (paramsContainer.querySelector('.text-muted')) {
        paramsContainer.innerHTML = '';
    }

    const paramDiv = document.createElement('div');
    paramDiv.className = 'border rounded p-2 mb-2 d-flex justify-content-between align-items-center bg-light';
    paramDiv.innerHTML = `
        <span><strong>${paramName}:</strong> ${paramValue}</span>
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove(); checkParamsEmpty()">
            <i class="bi bi-trash"></i>
        </button>
    `;
    paramsContainer.appendChild(paramDiv);

    // 清空输入框
    document.getElementById('param-name').value = '';
    document.getElementById('param-value').value = '';

    showAlert(`已添加技术参数: ${paramName}`, 'success');
}

// 检查参数容器是否为空
function checkParamsEmpty() {
    const paramsContainer = document.getElementById('technical-params');
    if (paramsContainer && paramsContainer.children.length === 0) {
        paramsContainer.innerHTML = `
            <div class="text-muted text-center py-2">
                <i class="bi bi-info-circle me-1"></i>
                添加的技术参数将显示在这里
            </div>
        `;
    }
}

// 评估文档质量
async function evaluateDocumentQuality() {
    const title = document.getElementById('doc-title').value;
    const content = document.getElementById('doc-content').value;
    const category = document.getElementById('doc-category').value;

    if (!title || !content) {
        showAlert('请先填写文档标题和内容', 'warning');
        return;
    }

    try {
        const response = await fetch('/api/v1/knowledge/evaluate-quality', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title: title,
                content: content,
                category: category
            })
        });

        const result = await response.json();

        if (result.success) {
            // 更新进度条
            document.getElementById('completeness-bar').style.width = result.completeness + '%';
            document.getElementById('accuracy-bar').style.width = result.accuracy + '%';
            document.getElementById('structure-bar').style.width = result.structure + '%';
            document.getElementById('overall-score').textContent = result.overall_score;
        }
    } catch (error) {
        console.error('质量评估失败:', error);
        // 使用模拟数据
        const completeness = Math.floor(Math.random() * 40) + 60;
        const accuracy = Math.floor(Math.random() * 30) + 70;
        const structure = Math.floor(Math.random() * 35) + 65;
        const overall = Math.floor((completeness + accuracy + structure) / 3);

        document.getElementById('completeness-bar').style.width = completeness + '%';
        document.getElementById('accuracy-bar').style.width = accuracy + '%';
        document.getElementById('structure-bar').style.width = structure + '%';
        document.getElementById('overall-score').textContent = overall;
    }
}

// 清洗文档数据
async function cleanDocumentData() {
    const content = document.getElementById('doc-content').value;
    if (!content.trim()) {
        showAlert('请先在"文档上传"标签页输入文档内容', 'warning');
        // 自动切换到文档上传标签页
        if (typeof switchDocTab === 'function') {
            switchDocTab('doc-upload');
        }
        return;
    }

    const config = {
        clean_terminology: document.getElementById('clean-terminology').checked,
        clean_units: document.getElementById('clean-units').checked,
        clean_format: document.getElementById('clean-format').checked,
        remove_duplicates: document.getElementById('remove-duplicates').checked,
        output_format: document.getElementById('output-format').value,
        content: content
    };

    try {
        const response = await fetch('/api/v1/knowledge/data/clean', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        const result = await response.json();

        if (result.success) {
            // 显示清洗预览
            const previewContainer = document.getElementById('cleaning-preview');
            previewContainer.innerHTML = `<pre>${result.cleaned_content}</pre>`;

            // 显示统计信息
            const statsContainer = document.getElementById('cleaning-stats');
            statsContainer.innerHTML = `
                <div>原始字符数: ${result.stats.original_length}</div>
                <div>清洗后字符数: ${result.stats.cleaned_length}</div>
                <div>清洗率: ${result.stats.cleaning_rate}%</div>
                <div>质量提升: ${result.stats.quality_improvement}%</div>
            `;

            showAlert('数据清洗完成', 'success');
        } else {
            showAlert('数据清洗失败: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('数据清洗失败:', error);

        // 降级处理：使用本地清洗演示
        const cleanedContent = performLocalCleaning(content, config);
        const previewContainer = document.getElementById('cleaning-preview');
        previewContainer.innerHTML = `
            <div class="alert alert-info mb-2">
                <small><i class="bi bi-info-circle me-1"></i>使用本地演示清洗</small>
            </div>
            <pre style="white-space: pre-wrap;">${cleanedContent}</pre>
        `;

        showAlert('已使用本地演示清洗功能', 'info');
    }
}

// 本地数据清洗演示
function performLocalCleaning(content, config) {
    let cleaned = content;

    if (config.clean_terminology) {
        // 电力术语标准化
        const terminologyMap = {
            '变电所': '变电站',
            '开关站': '开关站',
            '刀闸': '隔离开关'
        };

        Object.entries(terminologyMap).forEach(([old, new_]) => {
            cleaned = cleaned.replace(new RegExp(old, 'g'), new_);
        });
    }

    if (config.clean_units) {
        // 单位标准化
        cleaned = cleaned.replace(/千伏/g, 'kV');
        cleaned = cleaned.replace(/兆瓦/g, 'MW');
        cleaned = cleaned.replace(/千瓦/g, 'kW');
    }

    if (config.clean_format) {
        // 格式清理
        cleaned = cleaned.replace(/\s+/g, ' '); // 多个空格合并
        cleaned = cleaned.replace(/\n\s*\n/g, '\n'); // 多个换行合并
        cleaned = cleaned.trim();
    }

    if (config.remove_duplicates) {
        // 简单去重（去除重复的句子）
        const sentences = cleaned.split('。');
        const uniqueSentences = [...new Set(sentences)];
        cleaned = uniqueSentences.join('。');
    }

    return cleaned;
}

// 显示添加图片模态框
function showAddImageModal() {

    const modalElement = document.getElementById('addImageModal');
    if (!modalElement) {
        console.error('❌ 找不到addImageModal元素');
        alert('错误：找不到图片添加模态框');
        return;
    }

    try {
        // 重置表单
        resetImageUploadForm();

        // 使用自定义模态框显示方法
        showModal('addImageModal');

        // 初始化拖拽上传功能
        initImageDragDrop();

        // 初始化图片标注功能
        // initImageAnnotation(); // 暂时注释掉，使用新的标注系统

        // 初始化标签输入
        initializeImageTagInput();

    } catch (error) {
        console.error('❌ 显示图片模态框失败:', error);
        // 降级处理：直接显示模态框
        modalElement.style.display = 'block';
        modalElement.classList.add('show');
        alert('错误：无法显示图片添加模态框 - ' + error.message);
    }
}

// 重置图片上传表单
function resetImageUploadForm() {
    const form = safeGetElement('add-image-form', true);
    const fileInput = safeGetElement('img-file', true);
    const previewContainer = safeGetElement('image-preview-container', true);
    const previewList = safeGetElement('image-preview-list', true);
    const progressContainer = safeGetElement('upload-progress-container', true);
    const dropZone = safeGetElement('image-drop-zone', true);

    if (form) form.reset();
    if (fileInput) fileInput.value = '';
    if (previewList) previewList.innerHTML = '';

    safeClassListOperation(previewContainer, 'add', 'd-none');
    safeClassListOperation(progressContainer, 'add', 'd-none');

    // 重置拖拽区域
    if (dropZone) {
        safeClassListOperation(dropZone, 'remove', 'border-success');
        safeClassListOperation(dropZone, 'remove', 'bg-light');
        safeClassListOperation(dropZone, 'add', 'border-dashed');
    }
}

// 初始化图片拖拽上传功能
function initImageDragDrop() {
    const dropZone = document.getElementById('image-drop-zone');
    const fileInput = document.getElementById('img-file');

    // 检查元素是否存在
    if (!dropZone || !fileInput) {

        return;
    }

    // 点击上传区域触发文件选择
    dropZone.addEventListener('click', (e) => {
        if (e.target.tagName !== 'BUTTON') {
            fileInput.click();
        }
    });

    // 文件选择变化事件
    fileInput.addEventListener('change', handleImageSelection);

    // 拖拽事件
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('border-primary', 'bg-light');
        dropZone.classList.remove('border-dashed');
    });

    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-primary', 'bg-light');
        dropZone.classList.add('border-dashed');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-primary', 'bg-light');
        dropZone.classList.add('border-success');

        const files = Array.from(e.dataTransfer.files).filter(file =>
            file.type.startsWith('image/')
        );

        if (files.length > 0) {
            // 将文件添加到input元素
            const dt = new DataTransfer();
            files.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;

            handleImageSelection();
        } else {
            showAlert('请选择有效的图片文件', 'warning');
        }
    });
}

// 重绘画布
function redrawCanvas() {
    if (!currentImage) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(currentImage, 0, 0, canvas.width, canvas.height);

    // 绘制所有标注
    const annotations = getAnnotations();
    annotations.forEach(annotation => {
        drawAnnotation(annotation);
    });
}

// 绘制单个标注
function drawAnnotation(annotation) {
    ctx.strokeStyle = annotation.color;
    ctx.lineWidth = parseInt(annotation.width);
    ctx.strokeRect(
        annotation.startX,
        annotation.startY,
        annotation.endX - annotation.startX,
        annotation.endY - annotation.startY
    );

    // 绘制标注类型标签
    const label = annotation.type;
    ctx.fillStyle = annotation.color;
    ctx.font = '12px Arial';
    const textWidth = ctx.measureText(label).width;

    // 绘制标签背景
    ctx.fillRect(annotation.startX, annotation.startY - 20, textWidth + 6, 16);

    // 绘制标签文字
    ctx.fillStyle = 'white';
    ctx.fillText(label, annotation.startX + 3, annotation.startY - 8);
}

// 更新标注列表
function updateAnnotationList() {
    const listContainer = document.getElementById('annotation-list');
    if (!listContainer) return;

    if (annotations.length === 0) {
        listContainer.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-info-circle"></i>
                <p class="mt-2 small">在图片上绘制标注后将显示在这里</p>
            </div>
        `;
        return;
    }

    listContainer.innerHTML = '';

    annotations.forEach((annotation, index) => {
        const item = document.createElement('div');
        item.className = 'mb-2 p-2 border rounded';
        item.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span class="badge" style="background-color: ${annotation.color}">${annotation.type}</span>
                <button class="btn btn-sm btn-outline-danger" onclick="removeAnnotation(${index})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <small class="text-muted">
                位置: (${Math.round(annotation.startX)}, ${Math.round(annotation.startY)}) -
                (${Math.round(annotation.endX)}, ${Math.round(annotation.endY)})
            </small>
        `;
        listContainer.appendChild(item);
    });
}

// 移除标注
function removeAnnotation(index) {
    annotations.splice(index, 1);
    updateAnnotationList();
    redrawCanvas();
}

// 清除所有标注
function clearAnnotations() {
    annotations = [];
    updateAnnotationList();
    redrawCanvas();
}

// 保存当前标注
function saveCurrentAnnotation() {
    const description = document.getElementById('annotation-description').value;
    const severity = document.getElementById('annotation-severity').value;
    const confidence = document.getElementById('annotation-confidence').value;

    if (annotations.length === 0) {
        showAlert('没有标注可保存', 'warning');
        return;
    }

    // 为最后一个标注添加详细信息
    const lastAnnotation = annotations[annotations.length - 1];
    lastAnnotation.description = description;
    lastAnnotation.severity = severity;
    lastAnnotation.confidence = confidence;

    showAlert('标注信息已保存', 'success');

    // 清空表单
    document.getElementById('annotation-description').value = '';
    document.getElementById('annotation-severity').value = 'low';
    document.getElementById('annotation-confidence').value = '80';
    document.getElementById('confidence-value').textContent = '80%';
}

// 自动检测缺陷
async function autoDetectDefects() {
    if (!currentImage) {
        showAlert('请先选择图片', 'warning');
        return;
    }

    try {
        showAlert('AI缺陷检测功能开发中...', 'info');

        // 模拟自动检测结果
        setTimeout(() => {
            const mockDefects = [
                {
                    type: 'defect',
                    startX: 100,
                    startY: 100,
                    endX: 200,
                    endY: 150,
                    color: '#ff0000',
                    width: 2,
                    confidence: 0.85,
                    description: '疑似腐蚀缺陷'
                },
                {
                    type: 'defect',
                    startX: 300,
                    startY: 200,
                    endX: 380,
                    endY: 280,
                    color: '#ff0000',
                    width: 2,
                    confidence: 0.72,
                    description: '疑似过热点'
                }
            ];

            // 添加检测结果到标注列表
            mockDefects.forEach(defect => {
                annotations.push(defect);
            });

            updateAnnotationList();
            redrawCanvas();

            showAlert(`自动检测完成，发现 ${mockDefects.length} 个疑似缺陷`, 'success');
        }, 2000);

    } catch (error) {
        console.error('自动检测失败:', error);
        showAlert('自动检测失败: ' + error.message, 'error');
    }
}

// 开始图片智能处理
async function startImageProcessing() {
    if (!currentImage) {
        showAlert('请先选择图片', 'warning');
        return;
    }

    const progressContainer = document.getElementById('processing-progress');
    const progressBar = document.getElementById('processing-bar');
    const progressStep = document.getElementById('processing-step');
    const progressPercent = document.getElementById('processing-percent');

    // 显示进度
    progressContainer.style.display = 'block';

    try {
        const steps = [
            { name: 'OCR文字识别', enabled: document.getElementById('img-auto-ocr').checked },
            { name: '缺陷自动检测', enabled: document.getElementById('img-auto-analysis').checked },
            { name: '图像增强处理', enabled: document.getElementById('img-auto-enhance').checked },
            { name: '智能分类标注', enabled: document.getElementById('img-auto-classify').checked },
            { name: '元数据提取', enabled: document.getElementById('img-extract-metadata').checked }
        ];

        const enabledSteps = steps.filter(step => step.enabled);

        for (let i = 0; i < enabledSteps.length; i++) {
            const step = enabledSteps[i];
            const percent = Math.round(((i + 1) / enabledSteps.length) * 100);

            progressStep.textContent = step.name + '...';
            progressPercent.textContent = percent + '%';
            progressBar.style.width = percent + '%';

            // 模拟处理时间
            await new Promise(resolve => setTimeout(resolve, 1500));

            // 显示处理结果
            await showProcessingResult(step.name);
        }

        showAlert('智能处理完成！', 'success');

    } catch (error) {
        console.error('智能处理失败:', error);
        showAlert('智能处理失败: ' + error.message, 'error');
    } finally {
        // 隐藏进度
        setTimeout(() => {
            progressContainer.style.display = 'none';
        }, 2000);
    }
}

// 显示处理结果
async function showProcessingResult(stepName) {
    switch (stepName) {
        case 'OCR文字识别':
            const ocrResults = document.getElementById('ocr-results');
            const ocrText = document.getElementById('ocr-text');
            ocrResults.style.display = 'block';
            ocrText.textContent = '检测到的文字: 110kV变压器, 运行正常, 温度: 45°C';
            break;

        case '缺陷自动检测':
            const defectResults = document.getElementById('defect-results');
            const defectList = document.getElementById('defect-list');
            defectResults.style.display = 'block';
            defectList.innerHTML = `
                <div class="alert alert-warning alert-sm">
                    <i class="bi bi-exclamation-triangle"></i> 发现 2 个疑似缺陷点
                </div>
                <ul class="list-unstyled">
                    <li><span class="badge bg-danger">高</span> 腐蚀缺陷 (置信度: 85%)</li>
                    <li><span class="badge bg-warning">中</span> 过热点 (置信度: 72%)</li>
                </ul>
            `;
            break;

        case '智能分类标注':
            const classificationResults = document.getElementById('classification-results');
            const classificationTags = document.getElementById('classification-tags');
            classificationResults.style.display = 'block';
            classificationTags.innerHTML = `
                <span class="badge bg-primary me-1">变压器</span>
                <span class="badge bg-primary me-1">设备照片</span>
                <span class="badge bg-primary me-1">巡检</span>
                <span class="badge bg-warning me-1">缺陷</span>
            `;
            break;

        case '元数据提取':
            const metadataResults = document.getElementById('metadata-results');
            const metadataInfo = document.getElementById('metadata-info');
            metadataResults.style.display = 'block';
            metadataInfo.innerHTML = `
                <table class="table table-sm">
                    <tr><td>拍摄时间:</td><td>2024-01-15 10:30:25</td></tr>
                    <tr><td>相机型号:</td><td>Canon EOS R5</td></tr>
                    <tr><td>分辨率:</td><td>1920x1080</td></tr>
                    <tr><td>文件大小:</td><td>2.5 MB</td></tr>
                </table>
            `;
            break;
    }
}

// 添加图片
async function addImage() {
    const title = document.getElementById('img-title').value;
    const category = document.getElementById('img-category').value;
    const equipment = document.getElementById('img-equipment').value;
    const location = document.getElementById('img-location').value;
    const description = document.getElementById('img-description').value;
    const tags = document.getElementById('img-tags').value;
    const fileInput = document.getElementById('img-file');

    if (!title || !category) {
        showAlert('请填写图片标题和类别', 'warning');
        return;
    }

    if (fileInput.files.length === 0) {
        showAlert('请选择要上传的图片', 'warning');
        return;
    }

    try {
        const formData = new FormData();
        for (let i = 0; i < fileInput.files.length; i++) {
            formData.append('images', fileInput.files[i]);
        }
        formData.append('title', title);
        formData.append('category', category);
        formData.append('equipment', equipment);
        formData.append('location', location);
        formData.append('description', description);
        formData.append('tags', tags);

        const response = await fetch('/api/v1/knowledge/images', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showAlert('图片添加成功！', 'success');
            closeModal('addImageModal');
            // 重置表单
            document.getElementById('add-image-form').reset();
            document.getElementById('image-preview-container').classList.add('d-none');
            // 刷新知识库列表
            if (typeof refreshKnowledgeBase === 'function') {
                refreshKnowledgeBase();
            }
        } else {
            showAlert('添加失败: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('添加图片失败:', error);
        showAlert('添加图片失败', 'error');
    }
}

// 处理图片选择
function handleImageSelection() {
    const fileInput = document.getElementById('img-file');
    const files = Array.from(fileInput.files);

    if (files.length === 0) {
        resetImageUploadUI();
        return;
    }

    // 验证文件
    const validFiles = files.filter(file => {
        if (!file.type.startsWith('image/')) {
            showAlert(`文件 ${file.name} 不是有效的图片格式`, 'warning');
            return false;
        }
        if (file.size > 10 * 1024 * 1024) { // 10MB限制
            showAlert(`文件 ${file.name} 超过10MB大小限制`, 'warning');
            return false;
        }
        return true;
    });

    if (validFiles.length === 0) {
        resetImageUploadUI();
        return;
    }

    // 更新文件信息显示
    updateSelectedFilesInfo(validFiles);

    // 显示预览
    displayImagePreviews(validFiles);

    // 显示/隐藏批量选项
    toggleBatchOptions(validFiles.length > 1);

    // 如果只有一个文件，自动填充标题并加载到标注画布
    if (validFiles.length === 1) {
        const titleInput = document.getElementById('img-title');
        if (!titleInput.value) {
            titleInput.value = validFiles[0].name.replace(/\.[^/.]+$/, '');
        }

        // 加载图片到标注画布
        loadImageToAnnotationCanvas(validFiles[0]);

        // 提取图片元数据
        extractImageMetadata(validFiles[0]);
    } else {
        // 多文件时，设置通用标题
        const titleInput = document.getElementById('img-title');
        if (!titleInput.value) {
            titleInput.value = '批量上传图片';
        }

        // 清空标注画布
        clearAnnotationCanvas();
    }

    // 启用上传按钮
    const uploadBtn = document.getElementById('upload-image-btn');
    if (uploadBtn) uploadBtn.disabled = false;
}

// 加载图片到标注画布
function loadImageToAnnotationCanvas(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const img = new Image();
        img.onload = function() {
            // 调整画布大小
            const maxWidth = 800;
            const maxHeight = 600;
            let { width, height } = img;

            // 保持宽高比缩放
            if (width > maxWidth) {
                height = (height * maxWidth) / width;
                width = maxWidth;
            }
            if (height > maxHeight) {
                width = (width * maxHeight) / height;
                height = maxHeight;
            }

            canvas.width = width;
            canvas.height = height;

            // 绘制图片
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0, width, height);

            currentImage = img;
            annotations = [];
            updateAnnotationList();

            // 隐藏无图片消息
            const noImageMessage = document.getElementById('no-image-message');
            if (noImageMessage) {
                noImageMessage.style.display = 'none';
            }

            // 显示画布
            canvas.style.display = 'block';
        };
        img.src = e.target.result;
    };
    reader.readAsDataURL(file);
}

// 清空标注画布
function clearAnnotationCanvas() {
    if (canvas) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        canvas.style.display = 'none';
    }

    currentImage = null;
    annotations = [];
    updateAnnotationList();

    // 显示无图片消息
    const noImageMessage = document.getElementById('no-image-message');
    if (noImageMessage) {
        noImageMessage.style.display = 'block';
    }
}

// 提取图片元数据
function extractImageMetadata(file) {
    // 基本文件信息
    const resolutionInput = document.getElementById('img-resolution');
    const fileSizeInput = document.getElementById('img-file-size');
    const qualityBar = document.getElementById('img-quality-bar');
    const qualityText = document.getElementById('img-quality-text');

    if (fileSizeInput) {
        fileSizeInput.value = formatFileSize(file.size);
    }

    // 读取图片尺寸
    const reader = new FileReader();
    reader.onload = function(e) {
        const img = new Image();
        img.onload = function() {
            if (resolutionInput) {
                resolutionInput.value = `${img.width} x ${img.height}`;
            }

            // 计算质量评分
            const quality = calculateImageQuality(file, img);
            if (qualityBar && qualityText) {
                qualityBar.style.width = quality + '%';
                qualityText.textContent = `${quality}% - ${getQualityDescription(quality)}`;

                // 设置颜色
                if (quality >= 80) {
                    qualityBar.className = 'progress-bar bg-success';
                } else if (quality >= 60) {
                    qualityBar.className = 'progress-bar bg-warning';
                } else {
                    qualityBar.className = 'progress-bar bg-danger';
                }
            }
        };
        img.src = e.target.result;
    };
    reader.readAsDataURL(file);

    // 设置当前时间为拍摄时间（如果为空）
    const datetimeInput = document.getElementById('img-datetime');
    if (datetimeInput && !datetimeInput.value) {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000);
        datetimeInput.value = localDateTime.toISOString().slice(0, 16);
    }
}

// 计算图片质量评分
function calculateImageQuality(file, img) {
    let score = 0;

    // 分辨率评分 (40%)
    const pixels = img.width * img.height;
    if (pixels >= 1920 * 1080) score += 40;
    else if (pixels >= 1280 * 720) score += 30;
    else if (pixels >= 640 * 480) score += 20;
    else score += 10;

    // 文件大小评分 (30%)
    const sizeInMB = file.size / (1024 * 1024);
    if (sizeInMB >= 1 && sizeInMB <= 5) score += 30;
    else if (sizeInMB >= 0.5 && sizeInMB <= 8) score += 20;
    else score += 10;

    // 宽高比评分 (20%)
    const aspectRatio = img.width / img.height;
    if (aspectRatio >= 1.2 && aspectRatio <= 2.0) score += 20;
    else if (aspectRatio >= 0.8 && aspectRatio <= 2.5) score += 15;
    else score += 10;

    // 格式评分 (10%)
    if (file.type === 'image/jpeg' || file.type === 'image/png') score += 10;
    else score += 5;

    return Math.min(100, score);
}

// 获取质量描述
function getQualityDescription(quality) {
    if (quality >= 90) return '优秀';
    if (quality >= 80) return '良好';
    if (quality >= 70) return '一般';
    if (quality >= 60) return '较差';
    return '很差';
}

// 初始化知识库增强功能
function initializeKnowledgeEnhancements() {
    // 初始化文档文件选择处理
    handleDocumentFileSelect();

    // 初始化内容变化监听
    const contentTextarea = document.getElementById('doc-content');
    if (contentTextarea) {
        contentTextarea.addEventListener('input', function() {
            updateDocumentPreview(this.value);
        });
    }

    // 初始化处理选项变化监听
    const processingOptions = [
        'doc-auto-extract', 'doc-auto-classify',
        'doc-auto-keywords', 'doc-text-clean'
    ];

    processingOptions.forEach(id => {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.addEventListener('change', function() {
                const content = document.getElementById('doc-content').value;
                if (content) {
                    updateDocumentPreview(content);
                }
            });
        }
    });

}

    return {
        docModal: !!docModal,
        imgModal: !!imgModal,
        docBtn: !!docBtn,
        imgBtn: !!imgBtn,
        knowledgeBase: !!knowledgeBase
    };

// 切换到知识库页面的辅助函数
function switchToKnowledgeBase() {

    switchTab('knowledge-base');

    // 等待一下再测试
    setTimeout(() => {
        testKnowledgeBaseFunctions();
    }, 500);
}

// 重置图片上传UI
function resetImageUploadUI() {
    const previewContainer = document.getElementById('image-preview-container');
    const batchOptions = document.getElementById('batch-options');
    const uploadBtn = document.getElementById('upload-image-btn');
    const filesInfo = document.getElementById('selected-files-info');
    const uploadBtnText = document.getElementById('upload-btn-text');

    if (previewContainer) previewContainer.classList.add('d-none');
    if (batchOptions) batchOptions.style.display = 'none';
    if (uploadBtn) uploadBtn.disabled = true;
    if (filesInfo) filesInfo.textContent = '未选择文件';
    if (uploadBtnText) uploadBtnText.textContent = '上传图片';
}

// 更新选中文件信息
function updateSelectedFilesInfo(files) {
    const filesInfo = document.getElementById('selected-files-info');
    const uploadBtnText = document.getElementById('upload-btn-text');

    // 检查元素是否存在
    if (!filesInfo || !uploadBtnText) {

        return;
    }

    if (files.length === 1) {
        filesInfo.textContent = `已选择 1 张图片: ${files[0].name}`;
        uploadBtnText.textContent = '上传图片';
    } else {
        const totalSize = files.reduce((sum, file) => sum + file.size, 0);
        filesInfo.textContent = `已选择 ${files.length} 张图片 (总大小: ${formatFileSize(totalSize)})`;
        uploadBtnText.textContent = `批量上传 ${files.length} 张图片`;
    }
}

// 显示/隐藏批量选项
function toggleBatchOptions(show) {
    const batchOptions = document.getElementById('batch-options');
    const batchTitleHint = document.getElementById('batch-title-hint');

    if (batchOptions) {
        batchOptions.style.display = show ? 'block' : 'none';
    }

    if (batchTitleHint) {
        batchTitleHint.style.display = show ? 'inline' : 'none';
    }
}

// 显示图片预览
function displayImagePreviews(files) {
    const previewContainer = document.getElementById('image-preview-container');
    const previewList = document.getElementById('image-preview-list');

    previewList.innerHTML = '';
    previewContainer.classList.remove('d-none');

    files.forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const previewItem = document.createElement('div');
            previewItem.className = 'col-6 col-md-4';
            previewItem.innerHTML = `
                <div class="card">
                    <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">
                    <div class="card-body p-2">
                        <small class="text-muted">${file.name}</small>
                        <br>
                        <small class="text-muted">${formatFileSize(file.size)}</small>
                        <button type="button" class="btn btn-sm btn-outline-danger float-end"
                                onclick="removeImagePreview(${index})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            previewList.appendChild(previewItem);
        };
        reader.readAsDataURL(file);
    });
}

// 移除图片预览
function removeImagePreview(index) {
    const fileInput = document.getElementById('img-file');
    const files = Array.from(fileInput.files);

    files.splice(index, 1);

    // 更新文件输入
    const dt = new DataTransfer();
    files.forEach(file => dt.items.add(file));
    fileInput.files = dt.files;

    // 重新显示预览
    if (files.length > 0) {
        displayImagePreviews(files);
    } else {
        document.getElementById('image-preview-container').classList.add('d-none');
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 上传图片
async function uploadImages() {
    const fileInput = document.getElementById('img-file');
    const files = Array.from(fileInput.files);

    if (files.length === 0) {
        showAlert('请选择要上传的图片', 'warning');
        return;
    }

    // 检查是否使用单独元数据模式
    const individualMetadataCheckbox = document.getElementById('individual-metadata');
    const useIndividualMetadata = individualMetadataCheckbox ? individualMetadataCheckbox.checked : false;
    let formDataArray = [];

    if (useIndividualMetadata) {
        // 单独元数据模式：为每个文件收集单独的元数据
        formDataArray = collectIndividualMetadata(files);
        if (!formDataArray) {
            return; // 验证失败
        }
    } else {
        // 批量元数据模式：验证统一表单
        const title = document.getElementById('img-title').value.trim();
        const category = document.getElementById('img-category').value;

        if (!title) {
            showAlert('请输入图片标题', 'warning');
            return;
        }

        if (!category) {
            showAlert('请选择图片类别', 'warning');
            return;
        }

        // 获取基础表单数据
        const baseFormData = {
            title: title,
            category: category,
            equipment: document.getElementById('img-equipment').value.trim(),
            location: document.getElementById('img-location').value.trim(),
            description: document.getElementById('img-description').value.trim(),
            tags: document.getElementById('img-tags').value.trim(),
            autoOcr: document.getElementById('img-auto-ocr')?.checked || false,
            autoAnalysis: document.getElementById('img-auto-analysis')?.checked || false
        };

        // 为每个文件生成元数据
        formDataArray = files.map((file, index) => ({
            ...baseFormData,
            title: files.length > 1 ? `${baseFormData.title} (${index + 1}/${files.length})` : baseFormData.title
        }));
    }

    // 显示上传进度
    showBatchUploadProgress(true);
    const uploadBtn = document.getElementById('upload-image-btn');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';

    try {
        const results = [];
        const errors = [];

        // 更新总体进度信息
        updateOverallProgress(0, files.length);

        // 逐个上传文件
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const fileFormData = formDataArray[i];

            // 显示当前上传文件
            showCurrentUploadFile(file.name);

            try {
                const result = await uploadSingleImage(file, fileFormData);
                results.push({ file: file.name, success: true, result });

                // 添加成功结果到列表
                addUploadResult(file.name, true, '上传成功');

            } catch (error) {
                console.error(`上传文件 ${file.name} 失败:`, error);
                errors.push({ file: file.name, error: error.message });
                results.push({ file: file.name, success: false, error: error.message });

                // 添加失败结果到列表
                addUploadResult(file.name, false, error.message);
            }

            // 更新总体进度
            updateOverallProgress(i + 1, files.length);
            updateBatchUploadProgress(((i + 1) / files.length) * 100);
        }

        // 隐藏当前上传文件信息
        hideCurrentUploadFile();

        // 显示最终结果
        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;

        if (failCount === 0) {
            showAlert(`🎉 成功上传 ${successCount} 张图片！`, 'success');
        } else if (successCount === 0) {
            showAlert(`❌ 所有图片上传失败！`, 'danger');
        } else {
            showAlert(`⚠️ 部分上传完成：${successCount} 成功，${failCount} 失败`, 'warning');
        }

        // 如果全部成功，延迟关闭模态框
        if (failCount === 0) {
            setTimeout(() => {
                const modal = window.bootstrap.Modal.getInstance(document.getElementById('addImageModal'));
                modal.hide();
            }, 2000);
        }

        // 刷新图片列表（如果在图片管理页面）
        if (typeof refreshImageList === 'function') {
            refreshImageList();
        }

    } catch (error) {
        console.error('批量上传过程中发生错误:', error);
        showAlert(`批量上传失败: ${error.message}`, 'danger');
    } finally {
        // 重置上传按钮
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="bi bi-cloud-upload"></i> <span id="upload-btn-text">上传图片</span>';

        // 延迟隐藏进度条
        setTimeout(() => {
            showBatchUploadProgress(false);
        }, 5000);
    }
}

// 上传单张图片
async function uploadSingleImage(file, metadata) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('title', metadata.title);
    formData.append('category', metadata.category);
    formData.append('equipment', metadata.equipment);
    formData.append('location', metadata.location);
    formData.append('description', metadata.description);
    formData.append('tags', metadata.tags);
    formData.append('auto_ocr', metadata.autoOcr);
    formData.append('auto_analysis', metadata.autoAnalysis);

    const response = await fetch(`${API_BASE_URL}/upload/image`, {
        method: 'POST',
        body: formData
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    return await response.json();
}

// 显示/隐藏上传进度
function showUploadProgress(show) {
    const container = document.getElementById('upload-progress-container');
    if (show) {
        container.classList.remove('d-none');
    } else {
        container.classList.add('d-none');
    }
}

// 更新上传进度
function updateUploadProgress(percent, text) {
    const progressBar = document.getElementById('upload-progress-bar');
    const progressText = document.getElementById('upload-progress-text');

    if (progressBar) {
        progressBar.style.width = `${percent}%`;
        progressBar.setAttribute('aria-valuenow', percent);
    }

    if (progressText) {
        progressText.textContent = text || `${Math.round(percent)}%`;
    }
}

// ==================== 批量上传增强功能 ====================

// 初始化批量上传功能
function initBatchUpload() {
    // 批量选项事件监听器
    const useBatchMetadata = document.getElementById('use-batch-metadata');
    const individualMetadata = document.getElementById('individual-metadata');

    if (useBatchMetadata && individualMetadata) {
        // 互斥选择
        useBatchMetadata.addEventListener('change', function() {
            if (this.checked && individualMetadata) {
                individualMetadata.checked = false;
                toggleMetadataMode('batch');
            }
        });

        individualMetadata.addEventListener('change', function() {
            if (this.checked && useBatchMetadata) {
                useBatchMetadata.checked = false;
                toggleMetadataMode('individual');
            } else if (useBatchMetadata) {
                useBatchMetadata.checked = true;
                toggleMetadataMode('batch');
            }
        });
    } else {

    }

    // 模态框重置事件
    const addImageModal = document.getElementById('addImageModal');
    if (addImageModal) {
        addImageModal.addEventListener('hidden.bs.modal', function() {
            resetImageUploadModal();
        });
    }
}

// 切换元数据模式
function toggleMetadataMode(mode) {
    const batchForm = document.getElementById('add-image-form');
    const individualContainer = document.getElementById('individual-metadata-container');

    if (batchForm) {
        batchForm.style.display = mode === 'batch' ? 'block' : 'none';
    }

    if (individualContainer) {
        individualContainer.style.display = mode === 'individual' ? 'block' : 'none';

        // 生成单独的表单（如果是个别模式且有文件选择）
        if (mode === 'individual') {
            generateIndividualForms();
        }
    }
}

// 生成单独的元数据表单
function generateIndividualForms() {
    const fileInput = document.getElementById('img-file');
    const files = Array.from(fileInput.files);
    const container = document.getElementById('individual-forms-container');

    if (files.length === 0) return;

    container.innerHTML = '';

    files.forEach((file, index) => {
        const formHtml = `
            <div class="card mb-3" data-file-index="${index}">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-image"></i> ${file.name}
                        <small class="text-muted">(${formatFileSize(file.size)})</small>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">图片标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control individual-title"
                                       value="${file.name.replace(/\.[^/.]+$/, '')}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">图片类别 <span class="text-danger">*</span></label>
                                <select class="form-select individual-category" required>
                                    <option value="">请选择...</option>
                                    <option value="diagram">技术图纸</option>
                                    <option value="photo">设备照片</option>
                                    <option value="thermal">热成像图</option>
                                    <option value="defect">缺陷图片</option>
                                    <option value="maintenance">维护记录</option>
                                    <option value="inspection">巡检照片</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">关联设备</label>
                                <input type="text" class="form-control individual-equipment">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">拍摄位置</label>
                                <input type="text" class="form-control individual-location">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">图片描述</label>
                                <textarea class="form-control individual-description" rows="2"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">标签</label>
                                <input type="text" class="form-control individual-tags">
                            </div>
                            <div class="form-check">
                                <input class="form-check-input individual-auto-ocr" type="checkbox">
                                <label class="form-check-label">自动OCR识别</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input individual-auto-analysis" type="checkbox">
                                <label class="form-check-label">自动缺陷检测</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', formHtml);
    });
}

// 收集单独元数据
function collectIndividualMetadata(files) {
    const formDataArray = [];
    const container = document.getElementById('individual-forms-container');

    for (let i = 0; i < files.length; i++) {
        const card = container.querySelector(`[data-file-index="${i}"]`);
        if (!card) {
            showAlert(`找不到文件 ${files[i].name} 的元数据表单`, 'danger');
            return null;
        }

        const title = card.querySelector('.individual-title').value.trim();
        const category = card.querySelector('.individual-category').value;

        if (!title) {
            showAlert(`请为文件 ${files[i].name} 输入标题`, 'warning');
            return null;
        }

        if (!category) {
            showAlert(`请为文件 ${files[i].name} 选择类别`, 'warning');
            return null;
        }

        const formData = {
            title: title,
            category: category,
            equipment: card.querySelector('.individual-equipment').value.trim(),
            location: card.querySelector('.individual-location').value.trim(),
            description: card.querySelector('.individual-description').value.trim(),
            tags: card.querySelector('.individual-tags').value.trim(),
            autoOcr: card.querySelector('.individual-auto-ocr')?.checked || false,
            autoAnalysis: card.querySelector('.individual-auto-analysis')?.checked || false
        };

        formDataArray.push(formData);
    }

    return formDataArray;
}

// 重置图片上传模态框
function resetImageUploadModal() {
    // 重置文件输入
    const fileInput = document.getElementById('img-file');
    fileInput.value = '';

    // 重置UI状态
    resetImageUploadUI();

    // 重置表单
    const form = document.getElementById('add-image-form');
    if (form) {
        form.reset();
    }

    // 重置批量选项
    const useBatchMetadata = document.getElementById('use-batch-metadata');
    const individualMetadata = document.getElementById('individual-metadata');
    if (useBatchMetadata && individualMetadata) {
        useBatchMetadata.checked = true;
        individualMetadata.checked = false;
        toggleMetadataMode('batch');
    } else {

    }
}

// 显示批量上传进度
function showBatchUploadProgress(show) {
    const container = document.getElementById('upload-progress-container');
    const resultsContainer = document.getElementById('upload-results-container');
    const resultsList = document.getElementById('upload-results-list');

    if (!container) {

        return;
    }

    if (show) {
        container.classList.remove('d-none');
        // 重置进度显示
        updateBatchUploadProgress(0);
        updateOverallProgress(0, 0);
        if (resultsList) {
            resultsList.innerHTML = '';
        }
        if (resultsContainer) {
            resultsContainer.classList.add('d-none');
        }
    } else {
        container.classList.add('d-none');
    }
}

// 更新批量上传进度
function updateBatchUploadProgress(percent) {
    const progressBar = document.getElementById('upload-progress-bar');
    const progressText = document.getElementById('upload-progress-text');

    if (progressBar) {
        progressBar.style.width = percent + '%';
        progressBar.setAttribute('aria-valuenow', percent);

        if (percent >= 100) {
            progressBar.classList.remove('progress-bar-animated');
        }
    }

    if (progressText) {
        if (percent >= 100) {
            progressText.textContent = '上传完成！';
        } else {
            progressText.textContent = `${Math.round(percent)}%`;
        }
    }
}

// 更新总体进度信息
function updateOverallProgress(current, total) {
    const overallText = document.getElementById('overall-progress-text');
    if (overallText) {
        overallText.textContent = `${current}/${total}`;
    }
}

// 显示当前上传文件
function showCurrentUploadFile(fileName) {
    const currentInfo = document.getElementById('current-upload-info');
    const fileNameSpan = document.getElementById('current-file-name');

    if (fileNameSpan) {
        fileNameSpan.textContent = fileName;
    }
    if (currentInfo) {
        currentInfo.classList.remove('d-none');
    }
}

// 隐藏当前上传文件信息
function hideCurrentUploadFile() {
    const currentInfo = document.getElementById('current-upload-info');
    if (currentInfo) {
        currentInfo.classList.add('d-none');
    }
}

// 添加上传结果到列表
function addUploadResult(fileName, success, message) {
    const resultsList = document.getElementById('upload-results-list');
    const resultsContainer = document.getElementById('upload-results-container');

    if (!resultsList || !resultsContainer) {

        return;
    }

    // 显示结果容器
    resultsContainer.classList.remove('d-none');

    const resultItem = document.createElement('div');
    resultItem.className = `list-group-item d-flex justify-content-between align-items-center ${success ? 'list-group-item-success' : 'list-group-item-danger'}`;

    resultItem.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="bi ${success ? 'bi-check-circle-fill text-success' : 'bi-x-circle-fill text-danger'} me-2"></i>
            <span class="fw-bold">${fileName}</span>
        </div>
        <small class="text-muted">${message}</small>
    `;

    resultsList.appendChild(resultItem);

    // 滚动到最新结果
    try {
        resultItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    } catch (error) {

    }
}

// ==================== 图片管理功能 ====================

// 加载图片列表
async function loadImageList(page = 1, pageSize = 12) {
    try {

        // 显示加载状态
        const container = document.getElementById('images-container');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载图片...</p>
                </div>
            `;
        }

        // 模拟图片数据（实际应该从API获取）
        const mockImages = generateMockImageData();

        // 显示图片
        displayImageList(mockImages);

        // 更新统计信息
        updateImageStatistics(mockImages);

    } catch (error) {
        console.error('加载图片列表失败:', error);
        const container = document.getElementById('images-container');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="bi bi-exclamation-triangle"></i>
                    加载图片失败: ${error.message}
                </div>
            `;
        }
    }
}

// 生成模拟图片数据
function generateMockImageData() {
    const categories = ['diagram', 'photo', 'thermal', 'defect', 'maintenance', 'inspection'];
    const categoryNames = {
        'diagram': '技术图纸',
        'photo': '设备照片',
        'thermal': '热成像图',
        'defect': '缺陷图片',
        'maintenance': '维护记录',
        'inspection': '巡检照片'
    };

    const images = [];
    for (let i = 1; i <= 24; i++) {
        const category = categories[Math.floor(Math.random() * categories.length)];
        images.push({
            id: `img_${i}`,
            title: `设备图片 ${i}`,
            filename: `image_${i}.jpg`,
            category: category,
            categoryName: categoryNames[category],
            description: `这是第${i}张设备图片的描述信息`,
            equipment: `设备-${Math.floor(Math.random() * 10) + 1}`,
            location: `区域-${Math.floor(Math.random() * 5) + 1}`,
            tags: ['设备', '检查', category],
            uploadTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
            fileSize: Math.floor(Math.random() * 5000000) + 500000, // 0.5-5MB
            thumbnail: `https://picsum.photos/300/200?random=${i}`,
            fullImage: `https://picsum.photos/800/600?random=${i}`,
            metadata: {
                width: 1920,
                height: 1080,
                format: 'JPEG'
            }
        });
    }

    return images.sort((a, b) => new Date(b.uploadTime) - new Date(a.uploadTime));
}

// 显示图片列表
function displayImageList(images) {
    const container = document.getElementById('images-container');
    if (!container) return;

    if (images.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-images text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">暂无图片</h4>
                <p class="text-muted">点击"添加图片"按钮上传第一张图片</p>
                <button class="btn btn-primary" onclick="showAddImageModal()">
                    <i class="bi bi-plus-circle"></i> 添加图片
                </button>
            </div>
        `;
        return;
    }

    const viewMode = getCurrentViewMode();

    if (viewMode === 'grid') {
        displayGridView(images);
    } else {
        displayListView(images);
    }
}

// 网格视图
function displayGridView(images) {
    const container = document.getElementById('images-container');

    let html = '<div class="row g-3">';

    images.forEach(image => {
        html += `
            <div class="col-md-4 col-lg-3">
                <div class="card image-card h-100" onclick="showImageDetail('${image.id}')">
                    <div class="position-relative">
                        <img src="${image.thumbnail}" class="image-thumbnail" alt="${image.title}">
                        <span class="badge bg-primary category-badge">${image.categoryName}</span>
                        <div class="image-overlay">
                            <h6 class="mb-1">${image.title}</h6>
                            <small><i class="bi bi-calendar"></i> ${formatDate(image.uploadTime)}</small>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="bi bi-file-earmark"></i> ${formatFileSize(image.fileSize)}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" onclick="event.stopPropagation(); viewImage('${image.id}')">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="event.stopPropagation(); downloadImage('${image.id}')">
                                    <i class="bi bi-download"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// 列表视图
function displayListView(images) {
    const container = document.getElementById('images-container');

    let html = '<div class="list-group">';

    images.forEach(image => {
        html += `
            <div class="list-group-item list-group-item-action" onclick="showImageDetail('${image.id}')">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <img src="${image.thumbnail}" class="img-fluid rounded" style="height: 80px; width: 100%; object-fit: cover;">
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-1">${image.title}</h6>
                        <p class="mb-1 text-muted">${image.description}</p>
                        <small class="text-muted">
                            <span class="badge bg-secondary me-2">${image.categoryName}</span>
                            <i class="bi bi-calendar me-1"></i>${formatDate(image.uploadTime)}
                        </small>
                    </div>
                    <div class="col-md-2">
                        <small class="text-muted">
                            <i class="bi bi-file-earmark"></i> ${formatFileSize(image.fileSize)}<br>
                            <i class="bi bi-aspect-ratio"></i> ${image.metadata.width}×${image.metadata.height}
                        </small>
                    </div>
                    <div class="col-md-2 text-end">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="event.stopPropagation(); viewImage('${image.id}')">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="event.stopPropagation(); downloadImage('${image.id}')">
                                <i class="bi bi-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// 更新图片统计信息
function updateImageStatistics(images) {
    const totalImages = images.length;
    const totalSize = images.reduce((sum, img) => sum + img.fileSize, 0);
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentUploads = images.filter(img => new Date(img.uploadTime) > weekAgo).length;
    const categories = [...new Set(images.map(img => img.category))].length;

    // 更新统计显示
    const totalImagesEl = document.getElementById('total-images');
    const totalSizeEl = document.getElementById('total-size');
    const recentUploadsEl = document.getElementById('recent-uploads');
    const categoriesCountEl = document.getElementById('categories-count');

    if (totalImagesEl) totalImagesEl.textContent = totalImages;
    if (totalSizeEl) totalSizeEl.textContent = formatFileSize(totalSize);
    if (recentUploadsEl) recentUploadsEl.textContent = recentUploads;
    if (categoriesCountEl) categoriesCountEl.textContent = categories;
}

// 获取当前视图模式
function getCurrentViewMode() {
    const gridBtn = document.getElementById('grid-view-btn');
    return gridBtn && gridBtn.classList.contains('active') ? 'grid' : 'list';
}

// 设置视图模式
function setViewMode(mode) {
    const gridBtn = document.getElementById('grid-view-btn');
    const listBtn = document.getElementById('list-view-btn');

    if (gridBtn && listBtn) {
        if (mode === 'grid') {
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        } else {
            listBtn.classList.add('active');
            gridBtn.classList.remove('active');
        }

        // 重新加载图片列表
        loadImageList();
    }
}

// 显示图片详情
function showImageDetail(imageId) {

    // 获取图片数据（实际应该从API获取）
    const images = generateMockImageData();
    const image = images.find(img => img.id === imageId);

    if (!image) {
        showAlert('图片不存在', 'error');
        return;
    }

    // 填充模态框内容
    const modal = document.getElementById('imageDetailModal');
    const title = document.getElementById('image-detail-title');
    const img = document.getElementById('image-detail-img');
    const info = document.getElementById('image-detail-info');

    if (title) title.textContent = image.title;
    if (img) img.src = image.fullImage;

    if (info) {
        info.innerHTML = `
            <h6>基本信息</h6>
            <table class="table table-sm">
                <tr><td>文件名</td><td>${image.filename}</td></tr>
                <tr><td>类别</td><td><span class="badge bg-primary">${image.categoryName}</span></td></tr>
                <tr><td>大小</td><td>${formatFileSize(image.fileSize)}</td></tr>
                <tr><td>尺寸</td><td>${image.metadata.width} × ${image.metadata.height}</td></tr>
                <tr><td>格式</td><td>${image.metadata.format}</td></tr>
                <tr><td>上传时间</td><td>${formatDateTime(image.uploadTime)}</td></tr>
            </table>

            <h6 class="mt-3">详细信息</h6>
            <table class="table table-sm">
                <tr><td>关联设备</td><td>${image.equipment || '无'}</td></tr>
                <tr><td>拍摄位置</td><td>${image.location || '无'}</td></tr>
                <tr><td>描述</td><td>${image.description || '无'}</td></tr>
            </table>

            <h6 class="mt-3">标签</h6>
            <div>
                ${image.tags.map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('')}
            </div>
        `;
    }

    // 显示模态框
    const bsModal = new window.bootstrap.Modal(modal);
    bsModal.show();
}

// 查看图片（全屏）
function viewImage(imageId) {

    // 这里可以实现全屏查看功能
    showImageDetail(imageId);
}

// 下载图片
function downloadImage(imageId) {

    showAlert('下载功能开发中...', 'info');
}

// 编辑图片
function editImage() {
    showAlert('编辑功能开发中...', 'info');
}

// 删除图片
function deleteImage() {
    if (confirm('确定要删除这张图片吗？此操作不可恢复。')) {
        showAlert('删除功能开发中...', 'info');
    }
}

// 刷新图片列表
function refreshImageList() {
    loadImageList();
}

// 搜索图片
function searchImages() {
    const searchInput = document.getElementById('search-input');
    const query = searchInput ? searchInput.value.trim() : '';

    if (query) {

        showAlert(`搜索功能开发中，搜索词: ${query}`, 'info');
    } else {
        loadImageList();
    }
}

// 筛选图片
function filterImages() {
    const categoryFilter = document.getElementById('category-filter');
    const category = categoryFilter ? categoryFilter.value : '';

    showAlert(`筛选功能开发中，类别: ${category || '全部'}`, 'info');
}

// 排序图片
function sortImages() {
    const sortOrder = document.getElementById('sort-order');
    const order = sortOrder ? sortOrder.value : 'newest';

    showAlert(`排序功能开发中，排序: ${order}`, 'info');
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays}天前`;

    return date.toLocaleDateString('zh-CN');
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// ==================== 新版故障分析功能 ====================

// 故障分析步骤导航
function nextStep(stepNumber) {
    // 使用Bootstrap的Tab API进行切换
    const targetTab = document.querySelector(`#fault-analysis-tabs a[href="#step${stepNumber}"]`);
    if (targetTab) {
        // 使用Bootstrap的Tab实例进行切换
        const tab = new window.bootstrap.Tab(targetTab);
        tab.show();

    }
}

function previousStep(stepNumber) {
    nextStep(stepNumber);
}

// 生成分析报告
async function generateAnalysisReport() {
    // 收集所有步骤的数据
    const analysisData = {
        // 步骤1: 故障前运行方式
        system_mode: document.getElementById('system-mode')?.value || '',
        load_condition: document.getElementById('load-condition')?.value || '',
        weather_condition: document.getElementById('weather-condition')?.value || '',
        operation_time: document.getElementById('operation-time')?.value || '',
        operation_description: document.getElementById('operation-description')?.value || '',

        // 步骤2: 设备基本信息
        equipment_type: document.getElementById('equipment-type')?.value || '',
        equipment_number: document.getElementById('equipment-number')?.value || '',
        rated_voltage: document.getElementById('rated-voltage')?.value || '',
        manufacturer: document.getElementById('manufacturer')?.value || '',
        commissioning_date: document.getElementById('commissioning-date')?.value || '',
        last_maintenance: document.getElementById('last-maintenance')?.value || '',
        equipment_parameters: document.getElementById('equipment-parameters')?.value || '',

        // 步骤3: 现场设备检查情况
        visual_inspection: document.getElementById('visual-inspection')?.value || '',
        sound_check: document.getElementById('sound-check')?.value || '',
        smell_check: document.getElementById('smell-check')?.value || '',
        temperature_check: document.getElementById('temperature-check')?.value || '',
        other_site_conditions: document.getElementById('other-site-conditions')?.value || '',

        // 步骤4: 保护装置动作及故障录波情况
        protection_action: document.getElementById('protection-action')?.value || '',
        fault_recording: document.getElementById('fault-recording')?.value || '',
        relay_report: document.getElementById('relay-report')?.value || '',
        measurement_data: document.getElementById('measurement-data')?.value || '',

        // 步骤5: 现场解体检查
        disassembly_results: document.getElementById('disassembly-results')?.value || '',
        internal_inspection: document.getElementById('internal-inspection')?.value || '',
        test_data: document.getElementById('test-data')?.value || '',

        // 步骤6: 故障原因分析
        preliminary_analysis: document.getElementById('preliminary-analysis')?.value || '',
        cause_ranking: document.getElementById('cause-ranking')?.value || '',
        factor_analysis: document.getElementById('factor-analysis')?.value || '',

        // 步骤7: 下一步重点工作
        emergency_measures: document.getElementById('emergency-measures')?.value || '',
        repair_plan: document.getElementById('repair-plan')?.value || '',
        prevention_measures: document.getElementById('prevention-measures')?.value || '',
        monitoring_plan: document.getElementById('monitoring-plan')?.value || ''
    };

    // 验证必填字段
    if (!analysisData.equipment_type || !analysisData.equipment_number) {
        alert('请至少填写设备类型和设备编号');
        nextStep(2); // 跳转到设备信息步骤
        return;
    }

    // 显示加载状态
    const resultSection = document.getElementById('analysis-result-section');
    const loadingElement = document.getElementById('analysis-loading');
    const resultsElement = document.getElementById('analysis-results');

    if (resultSection) {
        resultSection.style.display = 'block';
    }
    if (loadingElement) {
        loadingElement.classList.remove('d-none');
    }
    if (resultsElement) {
        resultsElement.innerHTML = '';
    }

    try {
        const response = await fetch(`${API_BASE_URL}/fault-analysis/comprehensive`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(analysisData)
        });

        const data = await response.json();

        if (response.ok) {
            displayComprehensiveAnalysisResults(data);
        } else {
            throw new Error(data.error || '分析失败');
        }
    } catch (error) {
        console.error('故障分析错误:', error);
        // 显示模拟结果用于演示
        displayMockAnalysisResults(analysisData);
    } finally {
        document.getElementById('analysis-loading').classList.add('d-none');
    }
}

// 显示综合分析结果
function displayComprehensiveAnalysisResults(data) {
    const resultsHtml = `
        <div class="row">
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-primary"><i class="bi bi-exclamation-triangle"></i> 故障原因分析</h6>
                    <div class="border rounded p-3 bg-light">
                        ${data.fault_causes || '正在分析故障原因...'}
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary"><i class="bi bi-diagram-3"></i> 故障逻辑链条</h6>
                    <div class="border rounded p-3 bg-light">
                        ${data.fault_logic || '正在构建逻辑链条...'}
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-primary"><i class="bi bi-lightbulb"></i> 处理建议</h6>
                    <div class="border rounded p-3 bg-light">
                        ${data.treatment_suggestions || '正在生成处理建议...'}
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary"><i class="bi bi-shield-check"></i> 预防措施</h6>
                    <div class="border rounded p-3 bg-light">
                        ${data.prevention_measures || '正在制定预防措施...'}
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h6 class="text-primary"><i class="bi bi-clipboard-check"></i> 分析总结</h6>
            <div class="border rounded p-3 bg-light">
                ${data.summary || '正在生成分析总结...'}
            </div>
        </div>
    `;

    document.getElementById('analysis-results').innerHTML = resultsHtml;
}

// 显示模拟分析结果（用于演示）
function displayMockAnalysisResults(inputData) {
    const equipmentType = getEquipmentTypeText(inputData.equipment_type);
    const mockResults = {
        fault_causes: `
            <h6>主要故障原因：</h6>
            <ol>
                <li><strong>绝缘老化</strong> - 根据设备运行年限和检查结果，绝缘材料可能存在老化现象</li>
                <li><strong>过负荷运行</strong> - 长期重载运行导致设备温升过高</li>
                <li><strong>环境因素</strong> - 恶劣天气条件可能加速了故障的发生</li>
            </ol>
        `,
        fault_logic: `
            <div class="d-flex align-items-center justify-content-between mb-2">
                <span class="badge bg-primary">运行环境</span>
                <i class="bi bi-arrow-right"></i>
                <span class="badge bg-warning">设备老化</span>
                <i class="bi bi-arrow-right"></i>
                <span class="badge bg-danger">故障发生</span>
            </div>
            <p class="small text-muted">
                ${inputData.weather_condition ? '恶劣天气' : '正常天气'} →
                ${inputData.load_condition || '负荷运行'} →
                绝缘击穿 →
                保护动作 →
                设备跳闸
            </p>
        `,
        treatment_suggestions: `
            <h6>应急处理：</h6>
            <ul>
                <li>立即隔离故障设备，确保人员安全</li>
                <li>检查相关保护装置动作情况</li>
                <li>进行绝缘测试和油质分析</li>
            </ul>
            <h6>修复方案：</h6>
            <ul>
                <li>更换老化的绝缘部件</li>
                <li>清洁和检修内部结构</li>
                <li>进行全面的电气试验</li>
            </ul>
        `,
        prevention_measures: `
            <ul>
                <li>建立定期巡检制度，重点关注设备温升</li>
                <li>加强负荷监控，避免长期过载运行</li>
                <li>定期进行预防性试验和维护</li>
                <li>完善环境监测，及时应对恶劣天气</li>
            </ul>
        `,
        summary: `
            <p><strong>分析结论：</strong></p>
            <p>根据提供的信息，${equipmentType}故障主要由绝缘老化和过负荷运行引起。
            建议立即进行设备检修，更换老化部件，并加强日常维护管理。
            通过实施预防措施，可以有效避免类似故障的再次发生。</p>

            <p><strong>风险等级：</strong>
            <span class="badge bg-warning">中等风险</span></p>

            <p><strong>建议处理时间：</strong>
            <span class="text-danger">24小时内完成应急处理，7天内完成全面检修</span></p>
        `
    };

    displayComprehensiveAnalysisResults(mockResults);
}

// 导出分析报告
function exportAnalysisReport() {
    showAlert('导出功能开发中...', 'info');
}

// 保存分析结果
function saveAnalysisResult() {
    showAlert('保存功能开发中...', 'info');
}

// 重新分析
function resetAnalysis() {
    // 重置所有表单
    document.querySelectorAll('#fault-analysis-form input, #fault-analysis-form textarea, #fault-analysis-form select').forEach(element => {
        element.value = '';
    });

    // 隐藏结果区域
    const resultSection = document.getElementById('analysis-result-section');
    if (resultSection) {
        resultSection.style.display = 'none';
    }

    // 回到第一步
    nextStep(1);

    showAlert('已重置分析表单', 'success');
}

// 辅助函数：获取设备类型文本
function getEquipmentTypeText(type) {
    const typeMap = {
        'transformer': '变压器',
        'breaker': '断路器',
        'switch': '隔离开关',
        'capacitor': '电容器',
        'arrester': '避雷器',
        'cable': '电缆',
        'busbar': '母线',
        'generator': '发电机',
        'reactor': '电抗器',
        'line': '输电线路'
    };
    return typeMap[type] || type;
}

// ==================== 增强功能：文档和图片智能处理 ====================

// 添加图片（增强版本，包含智能标注和图像处理）
async function addImageWithAnnotation() {

    const title = document.getElementById('img-title')?.value || '';
    const category = document.getElementById('img-category')?.value || '';
    const equipmentType = document.getElementById('img-equipment-type')?.value || '';
    const equipment = document.getElementById('img-equipment')?.value || '';
    const location = document.getElementById('img-location')?.value || '';
    const description = document.getElementById('img-description')?.value || '';
    const tags = document.getElementById('img-tags')?.value || '';
    const fileInput = document.getElementById('img-file');

    if (!title || !category) {
        showAlert('请填写图片标题和类别', 'warning');
        return;
    }

    if (!fileInput || !fileInput.files || !fileInput.files.length) {
        showAlert('请选择要上传的图片', 'warning');
        return;
    }

    try {
        const formData = new FormData();
        formData.append('image', fileInput.files[0]);

        // 构建增强元数据
        const metadata = {
            title: title,
            category: category,
            equipment_type: equipmentType,
            equipment: equipment,
            location: location,
            description: description,
            tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
            annotations: getAnnotations(), // 获取标注数据
            ocr_enabled: true,
            defect_detection_enabled: true,
            image_enhancement_enabled: true
        };

        formData.append('metadata', JSON.stringify(metadata));

        const response = await fetch('/api/v1/knowledge/images/annotate', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showAlert('图片添加成功！已启用智能标注和图像处理', 'success');
            closeModal('addImageModal');
            // 重置表单和标注
            resetImageForm();
            clearAnnotations();
            // 刷新知识库列表
            if (typeof refreshKnowledgeBase === 'function') {
                refreshKnowledgeBase();
            }
        } else {
            showAlert('添加失败: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('添加图片失败:', error);
        showAlert('添加图片失败', 'error');
    }
}

// 图像标注相关变量
let currentAnnotationTool = 'rect';
let imageAnnotations = [];
let isDrawing = false;
let startX, startY;
let annotationCanvas, annotationCtx;

// 设置标注工具
function setAnnotationTool(tool) {
    currentAnnotationTool = tool;

    // 更新工具按钮状态
    document.querySelectorAll('[id$="-tool"]').forEach(btn => {
        btn.classList.remove('active');
    });
    const toolBtn = document.getElementById(tool + '-tool');
    if (toolBtn) {
        toolBtn.classList.add('active');
    }

}

// 获取当前标注数据
function getAnnotations() {
    return imageAnnotations || [];
}

// 清除所有标注
function clearAnnotations() {
    imageAnnotations = [];
    updateAnnotationList();
    if (annotationCanvas && annotationCtx) {
        annotationCtx.clearRect(0, 0, annotationCanvas.width, annotationCanvas.height);
        redrawImage();
    }

}

// 更新标注列表显示
function updateAnnotationList() {
    const listContainer = document.getElementById('annotation-list');
    if (!listContainer) return;

    if (imageAnnotations.length === 0) {
        listContainer.innerHTML = '<div class="text-muted"><i class="bi bi-info-circle"></i> 暂无标注</div>';
        return;
    }

    listContainer.innerHTML = imageAnnotations.map((annotation, index) => `
        <div class="border rounded p-2 mb-2">
            <div class="d-flex justify-content-between align-items-center">
                <span class="fw-bold">${annotation.type}</span>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAnnotation(${index})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <small class="text-muted">位置: (${Math.round(annotation.x)}, ${Math.round(annotation.y)})</small>
            ${annotation.label ? `<div><small>标签: ${annotation.label}</small></div>` : ''}
        </div>
    `).join('');
}

// 移除标注
function removeAnnotation(index) {
    imageAnnotations.splice(index, 1);
    updateAnnotationList();
    redrawAnnotationCanvas();
}

// 重绘画布
function redrawAnnotationCanvas() {
    if (!annotationCanvas || !annotationCtx) return;

    annotationCtx.clearRect(0, 0, annotationCanvas.width, annotationCanvas.height);
    redrawImage();

    // 重绘所有标注
    imageAnnotations.forEach(annotation => {
        drawAnnotation(annotation);
    });
}

// 绘制标注
function drawAnnotation(annotation) {
    if (!annotationCtx) return;

    annotationCtx.strokeStyle = '#007bff';
    annotationCtx.lineWidth = 2;
    annotationCtx.setLineDash([]);

    switch (annotation.type) {
        case 'rect':
            annotationCtx.strokeRect(annotation.x, annotation.y, annotation.width, annotation.height);
            break;
        case 'circle':
            annotationCtx.beginPath();
            annotationCtx.arc(annotation.x, annotation.y, annotation.radius, 0, 2 * Math.PI);
            annotationCtx.stroke();
            break;
        // 可以添加更多标注类型
    }
}

// 重绘图像
function redrawImage() {
    // 这里应该重新绘制原始图像
    // 实际实现需要保存原始图像数据
}

// OCR识别
async function runOCR() {
    const fileInput = document.getElementById('img-file');
    if (!fileInput.files.length) {
        showAlert('请先在"图片上传"标签页选择图片', 'warning');
        // 自动切换到图片上传标签页
        if (typeof switchImgTab === 'function') {
            switchImgTab('img-upload');
        }
        return;
    }

    try {
        const formData = new FormData();
        formData.append('image', fileInput.files[0]);

        const response = await fetch('/api/v1/knowledge/ocr', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            const ocrContainer = document.getElementById('ocr-results');
            if (ocrContainer) {
                ocrContainer.innerHTML = result.text || '<div class="text-muted">未识别到文字</div>';
            }
        } else {
            showAlert('OCR识别失败', 'error');
        }
    } catch (error) {
        console.error('OCR识别失败:', error);
        // 显示模拟结果
        const ocrContainer = document.getElementById('ocr-results');
        if (ocrContainer) {
            ocrContainer.innerHTML = '模拟OCR结果：变压器编号 T001，电压等级 110kV，运行状态正常';
        }
    }
}

// 缺陷检测
async function detectDefects() {
    const fileInput = document.getElementById('img-file');
    if (!fileInput.files.length) {
        showAlert('请先在"图片上传"标签页选择图片', 'warning');
        // 自动切换到图片上传标签页
        if (typeof switchImgTab === 'function') {
            switchImgTab('img-upload');
        }
        return;
    }

    try {
        const formData = new FormData();
        formData.append('image', fileInput.files[0]);

        const response = await fetch('/api/v1/knowledge/detect-defects', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success && result.defects) {
            // 添加检测到的缺陷作为标注
            result.defects.forEach(defect => {
                imageAnnotations.push({
                    type: 'rect',
                    x: defect.x,
                    y: defect.y,
                    width: defect.width,
                    height: defect.height,
                    label: defect.type,
                    confidence: defect.confidence
                });
            });
            updateAnnotationList();
            redrawAnnotationCanvas();
            showAlert(`检测到 ${result.defects.length} 个潜在缺陷`, 'info');
        } else {
            showAlert('未检测到明显缺陷', 'info');
        }
    } catch (error) {
        console.error('缺陷检测失败:', error);
        showAlert('缺陷检测失败', 'error');
    }
}

// 图像处理功能
function adjustImageBrightness(value) {

    // 检查是否有图片
    const fileInput = document.getElementById('img-file');
    if (!fileInput || !fileInput.files.length) {
        showAlert('请先在"图片上传"标签页选择图片', 'warning');
        if (typeof switchImgTab === 'function') {
            switchImgTab('img-upload');
        }
        return;
    }

    // 显示处理反馈
    showAlert(`亮度调整为: ${value > 0 ? '+' : ''}${value}`, 'info');

    // 这里可以添加实际的图像处理逻辑
    // 例如使用Canvas API处理图像
}

function adjustImageContrast(value) {

    // 检查是否有图片
    const fileInput = document.getElementById('img-file');
    if (!fileInput || !fileInput.files.length) {
        showAlert('请先在"图片上传"标签页选择图片', 'warning');
        if (typeof switchImgTab === 'function') {
            switchImgTab('img-upload');
        }
        return;
    }

    // 显示处理反馈
    showAlert(`对比度调整为: ${value}`, 'info');
}

function toggleImageFilter(filterType, enabled) {

    // 检查是否有图片
    const fileInput = document.getElementById('img-file');
    if (!fileInput || !fileInput.files.length) {
        showAlert('请先在"图片上传"标签页选择图片', 'warning');
        if (typeof switchImgTab === 'function') {
            switchImgTab('img-upload');
        }
        return;
    }

    // 显示处理反馈
    const filterNames = {
        'sharpen': '锐化处理',
        'denoise': '降噪处理',
        'edge': '边缘增强'
    };
    const filterName = filterNames[filterType] || filterType;
    showAlert(`${filterName}: ${enabled ? '已启用' : '已禁用'}`, 'info');
}

function resetImageProcessing() {
    // 重置所有图像处理参数
    const brightnessSlider = document.getElementById('brightness-slider');
    const contrastSlider = document.getElementById('contrast-slider');
    const sharpenFilter = document.getElementById('sharpen-filter');
    const noiseReduction = document.getElementById('noise-reduction');
    const edgeEnhance = document.getElementById('edge-enhance');

    if (brightnessSlider) brightnessSlider.value = 0;
    if (contrastSlider) contrastSlider.value = 1;
    if (sharpenFilter) sharpenFilter.checked = false;
    if (noiseReduction) noiseReduction.checked = false;
    if (edgeEnhance) edgeEnhance.checked = false;

}

function analyzeImageQuality() {
    // 模拟图像质量分析
    const clarity = Math.floor(Math.random() * 30) + 70;
    const saturation = Math.floor(Math.random() * 25) + 75;
    const noise = Math.floor(Math.random() * 40) + 10;
    const overall = Math.floor((clarity + saturation + (100 - noise)) / 3);

    const clarityBar = document.getElementById('clarity-bar');
    const saturationBar = document.getElementById('saturation-bar');
    const noiseBar = document.getElementById('noise-bar');
    const qualityScore = document.getElementById('image-quality-score');

    if (clarityBar) clarityBar.style.width = clarity + '%';
    if (saturationBar) saturationBar.style.width = saturation + '%';
    if (noiseBar) noiseBar.style.width = noise + '%';
    if (qualityScore) qualityScore.textContent = overall;
}

function exportProcessedImage(format) {

    showAlert(`图像已导出为 ${format.toUpperCase()} 格式`, 'success');
}

function exportAnnotations() {
    const annotations = getAnnotations();
    if (annotations.length === 0) {
        showAlert('暂无标注数据可导出', 'warning');
        return;
    }

    const dataStr = JSON.stringify(annotations, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'annotations.json';
    link.click();

    URL.revokeObjectURL(url);
    showAlert('标注数据已导出', 'success');
}

// 辅助函数
function addKeywordToTags(keyword) {
    const tagsInput = document.getElementById('doc-tags');
    if (tagsInput) {
        const currentTags = tagsInput.value;
        const newTags = currentTags ? `${currentTags}, ${keyword}` : keyword;
        tagsInput.value = newTags;
    }
}

function resetImageForm() {
    const form = document.getElementById('add-image-form');
    if (form) {
        form.reset();
    }

    const previewContainer = document.getElementById('image-preview-container');
    if (previewContainer) {
        previewContainer.classList.add('d-none');
    }
}

