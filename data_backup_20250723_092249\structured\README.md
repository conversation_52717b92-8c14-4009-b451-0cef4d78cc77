# 结构化数据目录

此目录用于存储经过结构化处理的数据，便于系统查询和分析。

## 目录结构

```
data/structured/
├── equipment/             # 设备信息数据
│   ├── transformers.json # 变压器数据
│   ├── breakers.json     # 断路器数据
│   └── cables.json       # 电缆数据
├── faults/               # 故障数据
│   ├── fault_cases.json  # 故障案例
│   ├── fault_patterns.json # 故障模式
│   └── solutions.json    # 解决方案
├── inspections/          # 检查数据
│   ├── inspection_records.json # 检查记录
│   ├── defect_types.json # 缺陷类型
│   └── maintenance_plans.json # 维护计划
├── operations/           # 运行数据
│   ├── switching_records.json # 倒闸记录
│   ├── operation_modes.json # 运行方式
│   └── risk_assessments.json # 风险评估
└── knowledge/            # 知识数据
    ├── technical_docs.json # 技术文档
    ├── standards.json    # 标准规范
    └── best_practices.json # 最佳实践
```

## 数据结构规范

### 设备信息结构
```json
{
  "equipment_id": "设备唯一标识",
  "name": "设备名称",
  "type": "设备类型",
  "model": "设备型号",
  "manufacturer": "制造商",
  "installation_date": "安装日期",
  "location": "安装位置",
  "status": "运行状态",
  "specifications": {
    "voltage_level": "电压等级",
    "capacity": "容量",
    "other_params": "其他参数"
  },
  "maintenance_history": [
    {
      "date": "维护日期",
      "type": "维护类型",
      "description": "维护描述",
      "result": "维护结果"
    }
  ]
}
```

### 故障案例结构
```json
{
  "fault_id": "故障唯一标识",
  "equipment_id": "相关设备ID",
  "fault_type": "故障类型",
  "occurrence_time": "发生时间",
  "symptoms": [
    "故障现象1",
    "故障现象2"
  ],
  "causes": [
    {
      "cause": "故障原因",
      "probability": "可能性",
      "evidence": "证据"
    }
  ],
  "diagnosis_process": [
    {
      "step": "诊断步骤",
      "method": "诊断方法",
      "result": "诊断结果"
    }
  ],
  "solutions": [
    {
      "solution": "解决方案",
      "effectiveness": "有效性",
      "cost": "成本",
      "time_required": "所需时间"
    }
  ],
  "lessons_learned": "经验教训"
}
```

### 检查记录结构
```json
{
  "inspection_id": "检查唯一标识",
  "equipment_id": "设备ID",
  "inspector": "检查人员",
  "inspection_date": "检查日期",
  "inspection_type": "检查类型",
  "checklist": [
    {
      "item": "检查项目",
      "status": "检查状态",
      "notes": "备注",
      "images": ["相关图片"]
    }
  ],
  "defects_found": [
    {
      "defect_type": "缺陷类型",
      "severity": "严重程度",
      "location": "缺陷位置",
      "description": "缺陷描述",
      "recommended_action": "建议措施"
    }
  ],
  "overall_condition": "整体状况",
  "next_inspection_date": "下次检查日期"
}
```

## 数据更新机制

1. **实时更新**：设备状态变化时立即更新
2. **批量更新**：定期批量处理新增数据
3. **版本控制**：保留数据变更历史
4. **数据校验**：确保数据完整性和一致性

## 查询接口

系统提供以下查询接口：

- 按设备类型查询设备信息
- 按故障类型查询故障案例
- 按时间范围查询检查记录
- 按风险等级查询运行数据
- 全文搜索知识库内容

## 数据备份

- 每日自动备份结构化数据
- 保留最近30天的备份文件
- 重要数据变更时立即备份
- 支持数据恢复和回滚操作
