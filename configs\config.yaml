# 故障分析智能助手系统配置文件

# 系统基础配置
system:
  name: "故障分析智能助手"
  version: "1.0.0"
  debug: true
  log_level: "INFO"
  timezone: "Asia/Shanghai"

# 服务器配置
server:
  host: "0.0.0.0"
  port: 5002
  workers: 4
  reload: true
  cors_origins:
    - "http://localhost:5002"
    - "http://127.0.0.1:5002"

# 数据库配置
database:
  url: "sqlite:///./fault_diagnosis.db"
  echo: false
  pool_size: 10
  max_overflow: 20

# 大语言模型配置（优化版）
llm:
  # DeepSeek模型配置
  deepseek:
    # R1推理模型配置
    r1_model: "deepseek-r1"
    chat_model: "deepseek-v3"
    api_key: "***********************************"
    base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    max_tokens: 8192
    temperature: 0.1             # 降低温度，提高技术分析准确性
    top_p: 0.9

    # 推理模式配置
    reasoning_mode:
      enable_thinking: true      # 启用思考过程
      show_reasoning: true       # 显示推理过程
      temperature: 0.3           # 推理模式温度

    # 故障分析专用配置
    fault_analysis:
      temperature: 0.1           # 故障分析低温度
      max_tokens: 12288          # 更大的输出长度
      enable_web_search: true    # 启用网络搜索

  # 本地模型配置（备选）
  local:
    model_path: "./models/deepseek-7b-chat"
    device: "cuda"
    max_length: 4096
    do_sample: true

# 嵌入模型配置（优化版）
embedding:
  model_name: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
  model_path: ""  # 空字符串表示使用在线模型
  dimension: 384
  batch_size: 32
  device: "cpu"
  # 镜像源配置
  hf_mirror: "https://hf-mirror.com"
  use_fallback: true  # 启用备选方案

  # 向量化优化配置
  normalize_embeddings: true
  use_gpu: false

  # 文本预处理配置
  enable_power_term_normalization: true
  preserve_technical_symbols: true

# 向量数据库配置（优化版）
vector_db:
  type: "chroma"               # 使用Chroma作为主要向量数据库
  index_path: "./embeddings/faiss_store"
  index_type: "IndexHNSWFlat"  # 使用HNSW索引，平衡速度和精度
  similarity_threshold: 0.7
  top_k: 10

  # Chroma数据库配置
  chroma:
    persist_directory: "./embeddings/chroma_store"
    collection_name: "baiyin_power_fault_collection"
    embedding_model: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    anonymized_telemetry: false  # 内网安全要求

  # FAISS索引优化配置（备选方案）
  faiss:
    hnsw_m: 16                   # HNSW连接数
    hnsw_ef_construction: 200    # 构建时搜索参数
    hnsw_ef_search: 100          # 搜索时参数
    auto_index_selection: true   # 根据数据规模自动选择索引类型

# 知识库配置（优化版）
knowledge_base:
  text_path: "./knowledge_base/text"
  images_path: "./knowledge_base/images"
  mappings_path: "./knowledge_base/mappings"
  chunk_size: 500              # 优化块大小，提高检索精度
  chunk_overlap: 50            # 减少重叠，避免冗余
  supported_formats:
    text: [".txt", ".md", ".pdf", ".docx", ".html"]
    image: [".jpg", ".jpeg", ".png", ".bmp", ".tiff"]

  # 智能分块配置
  enable_smart_chunking: true  # 启用智能分块
  preserve_structure: true     # 保留文档结构
  section_aware: true          # 章节感知分块

  # 多模态检索配置
  multimodal_search:
    enable_image_ocr: true     # 启用图像OCR
    combine_text_image: true   # 结合文本和图像检索
    image_weight: 0.3          # 图像检索权重
    text_weight: 0.7           # 文本检索权重

# 数据处理配置（增强版）
data_processing:
  raw_data_path: "./data/01_raw"
  structured_data_path: "./data/02_processed/structured"
  processed_data_path: "./data/04_production/cached"

  # 文本处理配置
  text:
    chunk_size: 500
    chunk_overlap: 50
    max_chunk_size: 1000
    enable_smart_chunking: true
    preserve_structure: true

  # 数据标准化配置
  standardization:
    enable_power_terms_mapping: true
    enable_unit_normalization: true
    enable_format_cleaning: true

  # 数据脱敏配置
  privacy:
    enable_anonymization: true
    preserve_technical_terms: true
    anonymize_personal_info: true
    anonymize_location_details: true
    region_mapping:
      白银: "BY"
      兰州: "LZ"
      天水: "TS"

  # OCR配置
  ocr:
    engine: "easyocr"  # paddleocr, tesseract, easyocr (使用easyocr更稳定)
    languages: ["zh-cn", "en"]  # 修正语言代码
    confidence_threshold: 0.8
    fallback_enabled: true  # 启用备选方案

  # 图像处理配置
  image:
    max_size: [1920, 1080]
    quality: 85
    formats: ["jpg", "png"]

# 向量处理配置
vector_processing:
  model_name: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
  model_path: ""  # 空字符串表示使用在线模型
  dimension: 384
  batch_size: 32
  device: "cpu"

  # 向量数据库类型选择
  vector_db_type: "chroma"  # chroma 或 faiss

  # Chroma配置
  chroma_path: "./embeddings/chroma_store"
  chroma_collection_name: "baiyin_power_fault_collection"

  # 向量化优化
  normalize_embeddings: true
  use_gpu: false

  # 备选方案配置
  use_fallback: true  # 启用TF-IDF备选方案
  fallback_vectorizer: "tfidf"

# LangChain配置
langchain:
  tools_path: "./langchain_modules/tools"
  chains_path: "./langchain_modules/chains"
  prompts_path: "./langchain_modules/prompts"
  
  # 链配置（优化版）
  chains:
    fault_analysis:
      max_iterations: 5
      verbose: true

    document_qa:
      retrieval_k: 10            # 增加检索数量
      score_threshold: 0.6       # 降低阈值，增加召回

  # RAG检索优化配置
  rag_retrieval:
    # 多策略检索
    enable_multi_strategy: true
    strategies:
      vector_search:
        weight: 0.6
        top_k: 20
      keyword_search:
        weight: 0.3
        top_k: 10
      semantic_search:
        weight: 0.1
        top_k: 5

    # 查询优化
    query_expansion:
      enable: true
      max_expansions: 3
      power_domain_terms: true   # 电力领域术语扩展

    # 结果重排序
    reranking:
      enable: true
      algorithm: "cross_encoder"  # 交叉编码器重排序
      top_k_rerank: 50
      final_top_k: 10

    # 上下文构建
    context_building:
      max_context_length: 4000
      preserve_structure: true
      add_metadata: true

# 提示词模板配置
prompts:
  fault_analysis: |
    你是一个专业的电力设备故障分析专家。请根据以下信息分析故障原因：
    
    设备信息：{equipment_info}
    故障现象：{fault_symptoms}
    检查结果：{inspection_results}
    历史数据：{historical_data}
    
    请提供：
    1. 可能的故障原因分析
    2. 故障发生的逻辑链条
    3. 下一步建议的检查或处理措施
    
  equipment_info: |
    请提取以下设备的关键信息：
    设备名称、型号、参数、投运时间、检修历史等
    
    原始数据：{raw_data}
    
  operation_analysis: |
    请分析故障前的运行方式：
    
    操作记录：{operation_logs}
    运行数据：{operation_data}
    录波文件：{waveform_data}
    
    请重构事件链并标记异常趋势。

# 外部接口配置
external_apis:
  scada:
    url: "${SCADA_API_URL}"
    username: "${SCADA_USERNAME}"
    password: "${SCADA_PASSWORD}"
    timeout: 30
  
  waveform:
    url: "${WAVEFORM_API_URL}"
    api_key: "${WAVEFORM_API_KEY}"
    timeout: 60

# 文件上传配置
upload:
  max_file_size: 100  # MB
  allowed_extensions:
    - ".pdf"
    - ".docx"
    - ".txt"
    - ".jpg"
    - ".png"
    - ".csv"
    - ".xlsx"
  upload_path: "./data/01_raw/uploads"

# 缓存配置
cache:
  type: "redis"
  url: "redis://localhost:6379/0"
  ttl: 3600  # 秒
  max_connections: 10

# 日志配置
logging:
  level: "WARNING"                # 生产环境使用WARNING级别
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
  rotation: "1 day"
  retention: "30 days"
  file_path: "./logs/app.log"
  # 生产环境日志配置
  production:
    enable_debug_logs: false      # 关闭调试日志
    enable_performance_logs: true # 启用性能日志
    max_log_size: "100MB"        # 单个日志文件最大大小

# 监控配置
monitoring:
  enable_metrics: true
  metrics_port: 9090
  health_check_interval: 30

# 安全配置
security:
  secret_key: "${SECRET_KEY}"
  algorithm: "HS256"
  access_token_expire_minutes: 30
  # 生产环境安全增强
  https_only: true                # 强制HTTPS
  secure_cookies: true            # 安全Cookie
  csrf_protection: true           # CSRF保护
  rate_limiting: true             # 速率限制
  max_requests_per_minute: 100    # 每分钟最大请求数
  
# 功能开关
features:
  enable_multimodal_search: true
  enable_auto_knowledge_update: true
  enable_real_time_sync: false
  enable_gpu_acceleration: false
