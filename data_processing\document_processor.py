#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档处理器 - 数据清洗、标注和格式化
"""

import re
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import hashlib

class DocumentProcessor:
    """文档处理器类"""
    
    def __init__(self):
        self.equipment_types = {
            '变压器': ['变压器', '主变', '配变', '电力变压器', '配电变压器'],
            '断路器': ['断路器', 'SF6断路器', '真空断路器', '油断路器'],
            '电缆': ['电缆', '电力电缆', 'XLPE电缆', '交联电缆'],
            '发电机': ['发电机', '汽轮发电机', '水轮发电机', '风力发电机'],
            '继电保护': ['继电保护', '保护装置', '距离保护', '差动保护'],
            '开关设备': ['隔离开关', 'GIS', '开关柜', '环网柜'],
            '互感器': ['电流互感器', '电压互感器', 'CT', 'PT'],
            '输电线路': ['输电线路', '架空线路', '电力线路', '高压线路']
        }
        
        self.fault_types = {
            '绝缘故障': ['绝缘击穿', '绝缘老化', '绝缘缺陷', '局部放电'],
            '机械故障': ['机械损伤', '拒动', '误动', '卡涩'],
            '过热故障': ['过热', '温升', '接头过热', '导线过载'],
            '短路故障': ['短路', '接地故障', '相间短路', '单相接地'],
            '开路故障': ['断线', '接触不良', '导体断裂', '连接松动'],
            '腐蚀故障': ['腐蚀', '氧化', '化学腐蚀', '电化学腐蚀']
        }
        
        self.severity_levels = {
            '轻微': ['轻微', '一般', '普通'],
            '中等': ['中等', '较重', '严重'],
            '严重': ['严重', '重大', '特别严重', '危急']
        }

    def clean_markdown_content(self, content: str) -> str:
        """清洗Markdown内容，转换为纯文本"""
        if not content:
            return ""
        
        # 移除Markdown标题标记
        content = re.sub(r'^#{1,6}\s+', '', content, flags=re.MULTILINE)
        
        # 移除粗体和斜体标记
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)
        content = re.sub(r'\*(.*?)\*', r'\1', content)
        
        # 移除列表标记
        content = re.sub(r'^[\s]*[-\*\+]\s+', '• ', content, flags=re.MULTILINE)
        content = re.sub(r'^[\s]*\d+\.\s+', '', content, flags=re.MULTILINE)
        
        # 移除代码块标记
        content = re.sub(r'```[\s\S]*?```', '', content)
        content = re.sub(r'`([^`]+)`', r'\1', content)
        
        # 移除链接标记
        content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)
        
        # 清理多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 移除行首行尾空白
        lines = [line.strip() for line in content.split('\n')]
        content = '\n'.join(line for line in lines if line)
        
        return content.strip()

    def extract_structured_info(self, content: str) -> Dict[str, Any]:
        """从内容中提取结构化信息"""
        info = {
            'equipment_type': '未知',
            'fault_type': '未知',
            'severity': '未知',
            'date': '未知',
            'location': '未知',
            'case_number': '未知',
            'keywords': [],
            'summary': ''
        }
        
        content_lower = content.lower()
        
        # 提取设备类型
        for eq_type, keywords in self.equipment_types.items():
            if any(keyword in content_lower for keyword in keywords):
                info['equipment_type'] = eq_type
                break
        
        # 提取故障类型
        for fault_type, keywords in self.fault_types.items():
            if any(keyword in content_lower for keyword in keywords):
                info['fault_type'] = fault_type
                break
        
        # 提取严重程度
        for severity, keywords in self.severity_levels.items():
            if any(keyword in content_lower for keyword in keywords):
                info['severity'] = severity
                break
        
        # 提取日期
        date_patterns = [
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})'
        ]
        for pattern in date_patterns:
            match = re.search(pattern, content)
            if match:
                info['date'] = match.group(1)
                break
        
        # 提取位置信息
        location_patterns = [
            r'(白银[^，。\s]*)',
            r'([^，。\s]*变电站)',
            r'([^，。\s]*电厂)',
            r'([^，。\s]*工业园区)'
        ]
        for pattern in location_patterns:
            match = re.search(pattern, content)
            if match:
                info['location'] = match.group(1)
                break
        
        # 提取案例编号
        case_patterns = [
            r'案例编号[：:]\s*([A-Z0-9-]+)',
            r'CASE-([A-Z0-9-]+)',
            r'编号[：:]\s*([A-Z0-9-]+)'
        ]
        for pattern in case_patterns:
            match = re.search(pattern, content)
            if match:
                info['case_number'] = match.group(1)
                break
        
        # 提取关键词
        keywords = set()
        for eq_type, kw_list in self.equipment_types.items():
            for kw in kw_list:
                if kw in content_lower:
                    keywords.add(kw)
        
        for fault_type, kw_list in self.fault_types.items():
            for kw in kw_list:
                if kw in content_lower:
                    keywords.add(kw)
        
        info['keywords'] = list(keywords)[:10]  # 限制关键词数量
        
        # 生成摘要（取前200字符）
        clean_content = self.clean_markdown_content(content)
        info['summary'] = clean_content[:200] + "..." if len(clean_content) > 200 else clean_content
        
        return info

    def calculate_quality_score(self, content: str, metadata: Dict[str, Any]) -> float:
        """计算内容质量分数"""
        score = 0.0
        
        # 内容长度评分 (0-30分)
        content_length = len(content)
        if content_length > 2000:
            score += 30
        elif content_length > 1000:
            score += 20
        elif content_length > 500:
            score += 10
        
        # 结构化程度评分 (0-25分)
        structure_indicators = ['##', '###', '**', '-', '1.', '2.', '3.']
        structure_count = sum(1 for indicator in structure_indicators if indicator in content)
        score += min(structure_count * 3, 25)
        
        # 专业术语评分 (0-25分)
        technical_terms = 0
        for eq_type, keywords in self.equipment_types.items():
            technical_terms += sum(1 for kw in keywords if kw in content.lower())
        for fault_type, keywords in self.fault_types.items():
            technical_terms += sum(1 for kw in keywords if kw in content.lower())
        score += min(technical_terms * 2, 25)
        
        # 完整性评分 (0-20分)
        completeness_indicators = ['故障现象', '故障分析', '处理措施', '经验总结', '预防措施']
        completeness_count = sum(1 for indicator in completeness_indicators if indicator in content)
        score += completeness_count * 4
        
        return min(score, 100.0)

    def process_document(self, title: str, content: str, source: str = 'unknown') -> Dict[str, Any]:
        """处理单个文档"""
        # 生成文档ID
        doc_id = hashlib.md5(f"{title}_{content[:100]}".encode()).hexdigest()[:12]
        
        # 清洗内容
        cleaned_content = self.clean_markdown_content(content)
        
        # 提取结构化信息
        structured_info = self.extract_structured_info(content)
        
        # 计算质量分数
        quality_score = self.calculate_quality_score(content, structured_info)
        
        # 构建处理后的文档
        processed_doc = {
            'id': doc_id,
            'title': title,
            'original_content': content,
            'cleaned_content': cleaned_content,
            'display_content': self.format_for_display(cleaned_content, structured_info),
            'metadata': {
                'source': source,
                'processed_at': datetime.now().isoformat(),
                'quality_score': quality_score,
                'content_length': len(content),
                'cleaned_length': len(cleaned_content),
                **structured_info
            },
            'tags': self.generate_tags(structured_info),
            'category': self.determine_category(structured_info)
        }
        
        return processed_doc

    def format_for_display(self, content: str, metadata: Dict[str, Any]) -> str:
        """格式化内容用于前端显示"""
        display_parts = []
        
        # 添加元数据信息卡片
        info_card = f"""
📋 案例信息
• 设备类型: {metadata.get('equipment_type', '未知')}
• 故障类型: {metadata.get('fault_type', '未知')}
• 严重程度: {metadata.get('severity', '未知')}
• 发生时间: {metadata.get('date', '未知')}
• 发生地点: {metadata.get('location', '未知')}
• 案例编号: {metadata.get('case_number', '未知')}

📝 内容摘要
{metadata.get('summary', '暂无摘要')}

📖 详细内容
{content}
        """.strip()
        
        return info_card

    def generate_tags(self, metadata: Dict[str, Any]) -> List[str]:
        """生成标签"""
        tags = []
        
        if metadata.get('equipment_type') != '未知':
            tags.append(metadata['equipment_type'])
        
        if metadata.get('fault_type') != '未知':
            tags.append(metadata['fault_type'])
        
        if metadata.get('severity') != '未知':
            tags.append(f"严重程度:{metadata['severity']}")
        
        if metadata.get('location') != '未知':
            tags.append(metadata['location'])
        
        # 添加关键词标签
        tags.extend(metadata.get('keywords', [])[:5])
        
        return list(set(tags))  # 去重

    def determine_category(self, metadata: Dict[str, Any]) -> str:
        """确定文档类别"""
        equipment_type = metadata.get('equipment_type', '未知')
        fault_type = metadata.get('fault_type', '未知')
        
        if equipment_type != '未知' and fault_type != '未知':
            return f"{equipment_type}_{fault_type}"
        elif equipment_type != '未知':
            return equipment_type
        elif fault_type != '未知':
            return fault_type
        else:
            return "其他"

    def batch_process_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量处理文档"""
        processed_docs = []
        
        for doc in documents:
            try:
                title = doc.get('title', '未知标题')
                content = doc.get('content', '')
                source = doc.get('source', 'unknown')
                
                if content:
                    processed_doc = self.process_document(title, content, source)
                    processed_docs.append(processed_doc)
                    
            except Exception as e:
                print(f"处理文档失败 {doc.get('title', 'unknown')}: {e}")
                continue
        
        return processed_docs

    def save_processed_documents(self, processed_docs: List[Dict[str, Any]], output_path: str):
        """保存处理后的文档"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump({
                'processed_at': datetime.now().isoformat(),
                'total_documents': len(processed_docs),
                'documents': processed_docs
            }, f, ensure_ascii=False, indent=2)
        
        print(f"已保存 {len(processed_docs)} 个处理后的文档到 {output_path}")

# 使用示例
if __name__ == '__main__':
    processor = DocumentProcessor()
    
    # 测试单个文档处理
    test_content = """
    # 110kV变压器套管故障案例分析
    
    ## 案例基本信息
    - **案例编号**: CASE-TR-001
    - **故障时间**: 2024年3月15日 14:30
    - **设备信息**: 110kV主变压器
    - **故障位置**: 白银第一变电站
    - **故障类型**: 套管绝缘击穿
    
    ## 故障现象
    变压器A相高压套管发生绝缘击穿，导致差动保护动作跳闸。
    """
    
    result = processor.process_document("测试案例", test_content, "test")
    print("处理结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
